import copy
import logging
import uuid
from traceback import print_exc
from uuid import UUID, uuid4

import pandas as pd
import pydash
from cachetools import TTLCache, cached
from celery import chain
from django.db import IntegrityError, transaction
from django.forms.models import model_to_dict
from django.utils import timezone
from rest_framework import status

import everstage_ddd.datasheet.selectors as ds_selectors
from commission_engine.accessors.client_accessor import (
    get_client_is_datasheet_sharded,
    get_client_subscription_plan,
    should_insert_meta_data_to_vec_db,
    should_revalidate_datasheet_on_update,
)
from commission_engine.accessors.databook_accessor import (
    DatabookAccessor,
    DatasheetAccessor,
    DatasheetVariableAccessor,
)
from commission_engine.accessors.etl_housekeeping_accessor import ETLLockAccessor
from commission_engine.accessors.hris_integration_accessor import HrisConfigAccessor
from commission_engine.database.snowflake_query_utils import (
    create_datasheet_data_table,
    delete_transformation_result_tables,
    invalidate_data_datasheet,
)
from commission_engine.services import etl_sync_status_service
from commission_engine.services.commission_calculation_service.reference_data_type import (
    get_data_types,
)
from commission_engine.services.databook_etl_sync_status_service import (
    current_all_locked_datasheets,
)
from commission_engine.services.datasheet_data_services.datasheet_retrieval_service import (
    pivot_and_sort_data,
    rename_pivot_columns,
)
from commission_engine.services.etl_global_sync_status_service import (
    EtlGlobalSyncStatusService,
)
from commission_engine.services.etl_sync_status_service import (
    get_recent_datasheet_sync_status,
)
from commission_engine.services.ever_object_service import (
    get_all_variables_for_object as get_report_object_variables,
)
from commission_engine.services.ever_object_service import get_ever_object_by_id
from commission_engine.services.ever_object_service import (
    get_primary_key as get_report_object_primary_key,
)
from commission_engine.services.ever_object_service import (
    get_report_object_variable_display_name,
)
from commission_engine.services.expression_designer import (
    TokenTypesV2,
    get_expression_format,
    is_window_function,
)
from commission_engine.types import HierarchyMetaData, HierarchyUtils
from commission_engine.utils.general_data import (
    COMMISSION_TYPE,
    DATASHEET_SOURCE,
    ETL_ACTIVITY,
    ETL_STATUS,
    SYNC_OBJECT,
    SegmentEvents,
    SegmentProperties,
)
from commission_engine.utils.general_utils import log_time_taken
from crystal.accessors.crystal_admin_accessor import CrystalAdminAccessor
from everstage_ddd.datasheet import DatasheetVariableTemp
from everstage_ddd.datasheet.data_models import (
    CreateDatasheetResponse,
    DatasheetCloneRequest,
    DatasheetCloneResponse,
    DatasheetDetail,
    DatasheetDetailCanvas,
    DatasheetDetailEdit,
    DatasheetGenerateFailureResponse,
    DatasheetGenerateRequest,
    DatasheetGenerateResponse,
    DatasheetGraphRequest,
    DatasheetGroup,
    DatasheetModel,
    DatasheetSourceOptions,
    DatasheetSyncStatusResponse,
    DatasheetValidateRequest,
    DatasheetValidateResponse,
    DatasheetVariableCloneRequest,
    DatasheetVariableSerializer,
    DatasheetVariablesList,
    DatasheetViewCloneRequest,
    DeleteDatasheetResponse,
    DsFilterOperatorsList,
    EmptyTransformationModel,
    ExportDatasheetRequest,
    TransformationsListModel,
    UpdateDatasheetResponse,
)
from everstage_ddd.datasheet.enums import (
    MAX_COLUMN_LIMIT,
    SAME_SOURCE_VAR,
    DataOrigin,
    DatasheetSourceType,
    GroupByAggDatatypesMap,
    TransformationType,
)
from everstage_ddd.datasheet.exceptions import DatasheetException
from everstage_ddd.datasheet.helpers.datasheet_validator import DatasheetValidator
from everstage_ddd.datasheet.helpers.datasheet_version_writer import (
    DatasheetVersionWriter,
)
from everstage_ddd.datasheet.helpers.etl_spec_transformer import ETLSpecTransformer
from everstage_ddd.datasheet.selectors import (
    DatabookSelector,
    DatasheetVariableSelector,
    DbkdPkdMapSelector,
    DsFilterOperatorSelector,
    StormBreakerSelector,
)
from everstage_ddd.datasheet.selectors.datasheet_selector import DatasheetSelector
from everstage_ddd.datasheet.services.databook_services import (
    create_databook,
    update_databook,
)
from everstage_ddd.datasheet.services.datasheet_variables import (
    datasheet_variables_fetch,
)
from everstage_ddd.datasheet.utils.datasheet_utils import (
    find_changed_keys_in_datasheet_meta,
    handle_datasheet_unique_constraint,
)
from everstage_ddd.global_search.meta_data_extractor.datasheet_meta_data import (
    delete_ds_meta_data_from_vector_db,
    upsert_ds_meta_data_in_vector_db,
)
from everstage_ddd.workflow_builder.service.workflow_service import (
    is_databook_datasheet_part_of_any_workflow,
)
from everstage_etl.tasks import report_etl
from everstage_etl.tasks.datasheet_generation.main import (
    get_datasheet_wrapper_sync_task,
)
from interstage_project.celery import TaskGroupEnum
from interstage_project.global_utils.localization_utils import (
    get_localized_message_utils,
)
from interstage_project.threadlocal_log_context import set_threadlocal_context
from interstage_project.utils import get_queue_name_respect_to_task_group
from spm.accessors.variable_accessor import VariableDataTypeAccessor
from spm.constants.audit_trail import AUDIT_TRAIL_EVENT_TYPE as EVENT
from spm.services import audit_services
from spm.services.analytics_services.analytics_service import CoreAnalytics

# Importing here locally to avoid circular imports
from spm.services.commission_plan_services import (
    is_datasheets_used_in_any_commission_plan,
    is_datasheets_used_in_any_settlement_rule,
)
from spm.services.custom_object_services.custom_object_service import (
    get_all_variables as get_all_custom_object_variables,
)
from spm.services.custom_object_services.custom_object_service import (
    get_custom_object_by_id,
    get_custom_object_variable_display_name,
)
from spm.services.custom_object_services.custom_object_service import (
    get_primary_key as get_custom_object_primary_key,
)
from spm.services.databook_services import (
    _construct_reason_for_generation_in_progress_delete_error,
    clone_datasheet,
    databook_names_by_ids,
    datasheet_source_details,
    get_datasheet_dependencies_details,
    get_hierarchy_reference_sheet_id,
    get_transformation_meta_data,
    is_hierarchy_calculated_field,
    modify_hierarchy_meta_data_for_key,
    record_databook_changes_on_delete,
)
from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph
from spm.services.datasheet_permission_services import (
    does_user_has_permission_to_custom_objects_in_datasheet,
    get_all_hidden_columns_in_a_datasheet_for_user,
    invalidate_datasheet_permissions,
)
from spm.services.rbac_services import does_user_have_databook_manage_permission
from superset.services.data_source_services import (
    check_if_ds_used_in_analytics,
    check_if_ds_used_in_analytics_and_update,
)

logger = logging.getLogger(__name__)
# ruff: noqa: TRY003
# ruff: noqa: PLR0915
# ruff: noqa: PLR0912


def compute_datasheet_order(client_id: int, datasheet_id: uuid.UUID | str):
    """
    Compute and update the datasheet order for the given datasheet_id
    """
    datasheet_graph = DataSheetGraph(
        client_id=client_id,
        include_stale_information_query=False,
    )
    datasheet_order_records = datasheet_graph.compute_datasheet_order(
        datasheet_id=str(datasheet_id)
    )
    # Updating the datasheet_order in bulk the database
    datasheet_accessor = DatasheetSelector(client_id=client_id)
    datasheet_accessor.bulk_datasheet_order_update(records=datasheet_order_records)


def get_datasheets_grouped_by_databook_id(
    client_id: int, user_id: str
) -> DatasheetGroup:
    """
    Get Datasheets Grouped By Databook ID
    """

    databook_groups = DatasheetSelector(client_id=client_id).get_databook_groups(
        user_id=user_id
    )

    return DatasheetGroup({"databooks": databook_groups})


def get_datasheets_grouped_by_archived_databooks(
    client_id: int, user_id: str
) -> DatasheetGroup:
    """
    Get Datasheets Grouped By Archived Databooks
    """
    databook_groups = DatasheetSelector(
        client_id=client_id
    ).get_archived_databook_groups(user_id=user_id)
    return DatasheetGroup({"archived_databooks": databook_groups})


def get_datasheets_grouped_by_commission_plans(
    client_id: int, login_user_id: str
) -> DatasheetGroup:
    """
    Get Datasheets Grouped By Commission Plans
    """

    commission_plan_groups = DatasheetSelector(
        client_id=client_id
    ).get_commission_plan_groups(login_user_id=login_user_id)

    return DatasheetGroup({"commission_plans": commission_plan_groups})


def _validate_datasheet_rename(
    client_id,
    databook_id,
    datasheet_id,
    updated_name,
):
    if updated_name == "":
        error_message = "Datasheet name cannot be empty"
        raise DatasheetException(
            code="DATASHEET_NAME_CANNOT_BE_EMPTY", message=error_message
        )

    ds_names = get_all_datasheet_names_in_databook(
        client_id=client_id, databook_id=databook_id
    )

    if is_datasheet_name_exists(updated_name, ds_names):
        error_message = f"Datasheet with name {updated_name.lower()} already exists."
        logger.info(error_message)  # type: ignore

        raise DatasheetException(
            code="DATASHEET_NAME_ALREADY_EXISTS", message=error_message
        )
    try:
        check_if_ds_used_in_analytics_and_update(
            client_id,
            databook_id,
            datasheet_id,
            updated_name,
        )
    except Exception as ex:
        error_message = f"Error in updating analytics for datasheet with datasheet_id {datasheet_id} and databook_id {databook_id}"
        logger.exception(error_message)
        raise DatasheetException(
            code="ANALYTICS_DATASHEET_UPLOAD_FAILED", message=error_message
        ) from ex
    logger.info(f"Datasheet updated name is valid {updated_name}")


@transaction.atomic
@handle_datasheet_unique_constraint
def update_datasheet_meta(
    datasheet_update_data, datasheet_update_data_audit
) -> UpdateDatasheetResponse:
    """
    Update a datasheet
    """
    client_id = datasheet_update_data_audit["client"]
    datasheet_id = datasheet_update_data["datasheet_id"]
    datasheet_selector = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )
    datasheet = datasheet_selector.get_datasheet(as_dict=True)
    databook_id = datasheet["databook_id"]
    updated_name = datasheet_update_data["name"]

    if updated_name is not None and updated_name.lower() != datasheet["name"].lower():
        updated_name = updated_name.strip()
        datasheet_update_data["name"] = updated_name
        _validate_datasheet_rename(
            client_id,
            databook_id,
            datasheet_id,
            updated_name,
        )

    updated_keys = find_changed_keys_in_datasheet_meta(datasheet_update_data, datasheet)

    if "name" in updated_keys and len(updated_keys) == 1:
        summary = f"Datasheet {datasheet['name']} renamed to {updated_name}"
    else:
        summary = (
            f"Datasheet columns {','.join(updated_keys)} updated in {datasheet['name']}"
        )

    if not updated_keys:
        logger.info("No meta data changed in datasheet %s", datasheet_id)
        return UpdateDatasheetResponse(status="SUCCESS", **datasheet)

    logger.info(summary)

    updated_data = {}

    for key in updated_keys:
        updated_data[key] = datasheet_update_data[key]

    updated_data = {**updated_data, **datasheet_update_data_audit}
    datasheet.get("additional_details", {}).update(
        datasheet_update_data_audit["additional_details"]
    )
    updated_data["additional_details"] = datasheet.get("additional_details", {})

    updated_datasheet = datasheet_selector.update_datasheet_meta(updated_data)

    # Audit log
    audit_services.log(
        client_id,
        EVENT["EDIT_DATASHEET"]["code"],
        datasheet_id,  # type: ignore
        summary,  # type: ignore
        datasheet_update_data_audit["created_by"],
        datasheet_update_data_audit["knowledge_begin_date"],  # type: ignore
        datasheet_update_data_audit["additional_details"],
    )
    logger.info(
        "Datasheet with datasheet_id %s is updated successfully - %s",
        datasheet_id,
        updated_datasheet,
    )

    return UpdateDatasheetResponse(status="SUCCESS", **updated_datasheet)


def is_datasheet_name_exists(databook_name, db_names):
    return databook_name.lower() in db_names


def get_all_datasheet_names(client_id: int) -> list[str]:
    """
    Get all datasheet names for a given client
    """
    return DatasheetSelector(client_id=client_id).get_all_datasheet_names()


def get_all_datasheet_names_in_databook(client_id: int, databook_id: UUID) -> list[str]:
    """
    Get all datasheet names for a given databook
    """
    return DatasheetSelector(client_id=client_id).get_all_datasheet_names_in_databook(
        databook_id
    )


@transaction.atomic
@handle_datasheet_unique_constraint
def create_datasheet(datasheet_meta_data) -> CreateDatasheetResponse:
    """
    Creates a datasheet in the databook if a databook id is passed.
    If databook id is not passed, it creates a new databook and then creates the datasheet in the new databook.

    Sample response:

    Without databook_id:
    {
        "status": "SUCCESS",
        "name": "Sheet90plj",
        "datasheetId": "dfd1956e-e94c-477d-95c5-4594e452ae63",
        "databookId": null,
        "databookName": null
    }

    With databook_id:
    {
        "status": "SUCCESS",
        "name": "Sheet90pljl",
        "datasheetId": "6327c632-7ec6-4fbb-a556-bfa283965c90",
        "databookId": "0b92b35d-8fbd-46b5-9526-fee477dc8c49",
        "databookName": "Untitled Databook(2)"
    }

    """
    client_id = datasheet_meta_data["client"]
    datasheet_meta_data["client_id"] = client_id
    datasheet_meta_data["datasheet_id"] = uuid4()
    databook_id = datasheet_meta_data["databook_id"]
    datasheet_name = datasheet_meta_data["name"].strip()
    databook_name = None

    if not databook_id:
        databook_meta_data = {
            **datasheet_meta_data,
            "client_id": client_id,
            "created_at": timezone.now(),
            "databook_id": uuid4(),
        }
        databook_meta_data["name"] = None
        databook_meta_data["datasheet_order"] = [datasheet_meta_data["datasheet_id"]]
        databook_details = create_databook(databook_meta_data)

        databook_id = databook_details.databookId
        databook_name = databook_details.name
        datasheet_meta_data["databook_id"] = databook_id
    else:
        databook_record = DatabookSelector(client_id=client_id).get_databook(
            databook_id=databook_id
        )
        databook_data = {
            "client_id": client_id,
            "additional_details": datasheet_meta_data["additional_details"],
            "knowledge_begin_date": datasheet_meta_data["knowledge_begin_date"],
            "created_by": datasheet_meta_data["created_by"],
            "name": databook_record.name,
            "databook_id": databook_id,
        }
        # Adding the new datasheet to the existing order in top
        existing_order = databook_record.datasheet_order or []
        databook_data["datasheet_order"] = existing_order + [
            str(datasheet_meta_data["datasheet_id"])
        ]
        update_databook(databook_data)

    if datasheet_name.strip() == "":
        raise DatasheetException(
            code="DATASHEET_NAME_CANNOT_BE_EMPTY",
            message="Datasheet name cannot be empty",
        )

    datasheet_meta_data["primary_key"] = get_primary_key_for_datasheet_source(
        client_id=client_id,
        source_type=datasheet_meta_data["source_type"],
        source_id=datasheet_meta_data["source_id"],
    )

    datasheet_meta_data["is_config_changed"] = True
    datasheet_meta_data["is_pk_modified"] = True

    if (
        datasheet_meta_data["source_type"] == DatasheetSourceType.DATASHEET.value
        and "source_databook_id" not in datasheet_meta_data
    ):
        raise DatasheetException(
            code="SOURCE_DATABOOK_ID_MISSING",
            message="source_databook_id is missing in the request",
        )

    validated_data = DatasheetModel(**datasheet_meta_data)

    ds_names = get_all_datasheet_names_in_databook(
        client_id=client_id, databook_id=databook_id
    )

    if is_datasheet_name_exists(datasheet_name, ds_names):
        error_message = f"Datasheet with name {datasheet_name.lower()} already exists."
        logger.info(error_message)  # type: ignore

        raise DatasheetException(
            code="DATASHEET_NAME_ALREADY_EXISTS", message=error_message
        )

    datasheet_id = validated_data.datasheet_id  # type: ignore

    datasheet_meta_data["datasheet_id"] = datasheet_id

    ordered_columns = create_ds_variables_and_order(datasheet_meta_data)

    validated_data = validated_data.model_copy(
        update={"ordered_columns": ordered_columns}
    )

    co_permission_for_datasheet = (
        does_user_has_permission_to_custom_objects_in_datasheet(
            client_id,
            databook_id,
            datasheet_id,
            datasheet_meta_data["created_by"],
            knowledge_date=None,
        )
    )

    if not co_permission_for_datasheet:
        logger.error(
            "You can't perform this action since you don't have access to the underlying data"
        )
        raise DatasheetException(
            code="NO_ACCESS_FOR_DATA",
            message="You can't perform this action since you don't have access to the underlying data",
            status=status.HTTP_403_FORBIDDEN,
        )

    try:
        datasheet_id = str(datasheet_id)

        DatasheetSelector(client_id=client_id).create_datasheet(
            validated_data.model_dump(by_alias=True)
        )

        # create a new table for the new datasheet if sharding is enabled for the client
        is_datasheet_data_sharded = get_client_is_datasheet_sharded(client_id=client_id)
        if is_datasheet_data_sharded:
            logger.info(
                f"Creating a new table for the datasheet {datasheet_id} for client {client_id}"
            )
            create_datasheet_data_table(client_id=client_id, datasheet_id=datasheet_id)

        try:
            logger.info("Checking if should insert meta data to vector db")
            if should_insert_meta_data_to_vec_db(client_id):
                logger.info(
                    f"Inserting metadata for datasheet {datasheet_id} for client {client_id}"
                )
                upsert_ds_meta_data_in_vector_db(
                    client_id,
                    databook_id,
                    datasheet_id,
                    add_stats=False,
                )
        except Exception as e:
            logger.info(
                f"Failed to insert metadata for datasheet {datasheet_id} for client {client_id}: {str(e)}"
            )

        # Audit log
        audit_services.log(
            client_id,
            EVENT["CREATE_DATASHEET"]["code"],
            datasheet_id,  # type: ignore
            datasheet_name,  # type: ignore
            datasheet_meta_data["created_by"],
            datasheet_meta_data["knowledge_begin_date"],  # type: ignore
            datasheet_meta_data["additional_details"],
        )
        compute_datasheet_order(client_id, datasheet_id)
        # Write the v1 datasheet transform spec parallelly for seamless switch
        DatasheetVersionWriter(
            client_id=client_id, datasheet_id=datasheet_id, version="v1"
        ).transform_and_save()
        logger.info(
            "Datasheet with datasheet_id {} and databook_id {} created successfully".format(
                datasheet_id, databook_id
            )
        )

        create_datasheet_response = {
            "status": "SUCCESS",
            "name": datasheet_name,
            "datasheetId": datasheet_id,
        }
        if databook_name:
            create_datasheet_response["databookId"] = databook_id
            create_datasheet_response["databookName"] = databook_name

        return CreateDatasheetResponse(**create_datasheet_response)
    except IntegrityError:
        raise
    except Exception:  # noqa: BLE001
        logger.exception(
            f"Error in creating Datasheet with datasheet_id {datasheet_id} and databook_id {databook_id}"
        )
        raise DatasheetException(
            code="INTERNAL_SERVER_ERROR",
            message="Internal server error",
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        ) from None


def create_ds_variables_and_order(datasheet_meta_data):
    client_id = datasheet_meta_data["client"]
    databook_id = datasheet_meta_data["databook_id"]
    datasheet_id = datasheet_meta_data["datasheet_id"]

    variables = datasheet_fetch_source_variables(
        client_id=client_id,
        source_type=datasheet_meta_data["source_type"],
        source_id=datasheet_meta_data["source_id"],
        only_selected=True,
    )

    ordered_columns = []

    ds_variables = []

    sysname_tags = {}

    primary_keys = datasheet_meta_data["primary_key"]

    for details in variables:  # type: ignore # noqa: RET503
        # if is_enriched_variable is True then we don't include the variable while creating datasheet
        if details.get("is_enriched_variable"):
            continue
        is_primary = details["system_name"] in primary_keys
        ds_var = {
            "knowledge_begin_date": datasheet_meta_data["knowledge_begin_date"],
            "client": client_id,
            "additional_details": datasheet_meta_data["additional_details"],
            "databook_id": databook_id,
            "datasheet_id": datasheet_id,
            "system_name": details["system_name"],
            "display_name": details["display_name"],
            "data_type": details["data_type_id"],
            "tags": (
                sysname_tags.get(details["system_name"])
                if details["system_name"] in sysname_tags
                else None
            ),
            "meta_data": None,
            "source_cf_meta_data": details.get("source_cf_meta_data", None),
            "field_order": 0,
            "variable_id": uuid4(),
            "source_variable_id": str(details["variable_id"]),
            "source_id": datasheet_meta_data["source_id"],
            "source_type": datasheet_meta_data["source_type"],
            "is_primary": is_primary,
            "is_selected": details.get("is_selected", True),
        }
        ordered_columns.append(details["system_name"])

        ds_variables.append(ds_var)

    ds_var_ser = DatasheetVariableSerializer(data=ds_variables, many=True)

    if ds_var_ser.is_valid():
        DatasheetVariableSelector(
            client_id=client_id, datasheet_id=datasheet_id
        ).create_ds_variables(ds_var_ser.validated_data)
    else:
        logger.exception(
            f"Error in creating Datasheet Variables for datasheet_id {datasheet_id} {ds_var_ser.errors}"
        )
        raise DatasheetException(
            code="DATASHEET_VARIABLE_CREATION_FAILED",
            message="Error in creating Datasheet Variables",
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    return ordered_columns


def get_primary_key_for_datasheet_source(client_id, source_type, source_id):
    if source_type == DatasheetSourceType.CUSTOM_OBJECT.value:
        return get_custom_object_primary_key(
            client_id=client_id, custom_object_id=source_id
        )["primary_key"]
    if source_type == DatasheetSourceType.REPORT_OBJECT.value:
        return get_report_object_primary_key(ever_object_id=source_id)
    if source_type == DatasheetSourceType.DATASHEET.value:
        return get_source_datasheet_primary_key(
            client_id=client_id, datasheet_id=source_id
        )["primary_key"]
    return None


def get_source_datasheet_primary_key(*, client_id: int, datasheet_id: UUID):
    """
    Returns the primary key for the datasheet id
    """
    return DatasheetSelector(client_id=client_id).get_primary_key(
        datasheet_id=datasheet_id
    )


def datasheet_fetch_source_variables(
    *, client_id, source_type, source_id, only_selected=False
) -> list[dict]:
    """
    Fetch variables of a datasheet from a specified source type.
    Source Type can be:
    1. Custom Object
    2. Report Object
    3. Datasheet

    Args:
        client_id: The client identifier.
        source_type: The type of the source from which variables are fetched.
        source_id: The identifier of the source.

    Returns:
        A list of dictionaries representing the variables of the datasheet.

    Raises:
        ValueError: If the source type is invalid.
    """
    if source_type == DatasheetSourceType.CUSTOM_OBJECT.value:
        result = get_all_custom_object_variables(
            client_id=client_id, custom_object_id=source_id
        )
    elif source_type == DatasheetSourceType.REPORT_OBJECT.value:
        result = get_report_object_variables(
            ever_object_id=source_id, client_id=client_id
        )
    elif source_type == DatasheetSourceType.DATASHEET.value:
        result = datasheet_variables_fetch(
            client_id=client_id, datasheet_id=source_id, only_selected=only_selected
        )
    else:
        raise ValueError(f"Invalid source type {source_type}")  # noqa: TRY003

    return result.model_dump(by_alias=True)


def get_datasheet_details(
    *, client_id: int, datasheet_id: UUID, logged_in_user: str
) -> DatasheetDetail:
    """
    Get datasheet details
    """
    datasheet_selector = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )
    details = datasheet_selector.get_datasheet_details(logged_in_user)
    return DatasheetDetail(**details)


def get_datasheet_details_for_canvas(
    *, client_id: int, datasheet_id: UUID, logged_in_user: str
) -> DatasheetDetailCanvas:
    """
    Get datasheet details
    """
    datasheet_selector = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )
    details = datasheet_selector.get_datasheet_details_for_canvas(logged_in_user)
    return DatasheetDetailCanvas(**details)


def get_datasheet_details_for_edit(
    *, client_id: int, datasheet_id: UUID
) -> DatasheetDetailEdit:
    """
    Get datasheet details
    """
    datasheet_selector = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )
    details = datasheet_selector.get_datasheet_details_for_edit()
    return DatasheetDetailEdit(**details)


def get_datasheet_sync_details(
    *, client_id: int, datasheet_id: UUID, databook_id: UUID
) -> DatasheetSyncStatusResponse:
    """
    Get datasheet sync status details
    """
    datasheet_sync_details = {}
    datasheet_sync_details = get_recent_datasheet_sync_status(
        client_id=client_id,
        datasheet_id=datasheet_id,
        databook_id=databook_id,
    )
    datasheet_sync_details["datasheet_id"] = datasheet_id
    datasheet_sync_details["databook_id"] = databook_id
    datasheet_sync_details["client_id"] = client_id
    return DatasheetSyncStatusResponse(**datasheet_sync_details)


@transaction.atomic
def get_export_datasheet_data_as_csv(
    export_datasheet_request: ExportDatasheetRequest,
    logged_in_user: str,
    updated_by: str,
):
    """
    Exports given datasheet's data to csv
    TODO: Split into smaller functions
    """
    databook_id = export_datasheet_request.databook_id
    datasheet_id = export_datasheet_request.datasheet_id
    apply_adjustments = export_datasheet_request.apply_adjustments
    client_id = export_datasheet_request.client_id
    datasheets = DatasheetAccessor(client_id).get_data_by_datasheet_id(
        databook_id, datasheet_id
    )
    ds_name = datasheets[0].name if len(datasheets) > 0 else ""
    db_name = DatabookAccessor(client_id).get_databook_by_id(databook_id).name
    data_output = "Post-adjustment" if apply_adjustments else "Pre-adjustment"
    from .datasheet_data import datasheet_data_fetch_for_export

    data = datasheet_data_fetch_for_export(export_datasheet_request, logged_in_user)
    logger.info("Request to export datsheet_id: {}".format(datasheet_id))

    ds_acc = DatasheetAccessor(client_id)
    data_source = ds_acc.get_data_by_datasheet_id(databook_id, datasheet_id)[0]
    try:
        # Desired datasheet column order
        ordered_datasheet_variables: list = DatasheetVariableSelector(
            client_id=client_id, datasheet_id=datasheet_id
        ).get_ordered_variables(datasheets[0].ordered_columns or [], logged_in_user)
        ordered_datasheet_columns = [
            ds_var.system_name for ds_var in ordered_datasheet_variables
        ]
        # The strombreaker services will return the data in Ascending order of columns names(system_name)
        # Here we again Rearranging the columns in the order given by ordered_datasheet_columns
        data = data[ordered_datasheet_columns]
    except PermissionError as _:
        logger.exception("Permission error while fetching data for export")
        return pd.DataFrame().to_csv(index=False), f"{data_source.name}.csv"
    dsv_acc = DatasheetVariableAccessor(client_id)
    ds_vars = dsv_acc.get_variables_for_db_ds(
        data_source.databook_id, data_source.datasheet_id, as_dicts=False
    )
    manage_permissions_enabled = does_user_have_databook_manage_permission(
        client_id, logged_in_user
    )

    hidden_columns_for_given_source_id = set()
    if manage_permissions_enabled is False:
        hidden_columns_for_given_source_id = set(
            get_all_hidden_columns_in_a_datasheet_for_user(
                client_id, datasheet_id, logged_in_user
            )
        )

    var_datatype_map = {}
    var_type_map = {}

    for var in ds_vars:
        if var.system_name not in hidden_columns_for_given_source_id:
            var_type_map[var.system_name] = var.display_name
            var_datatype_map[var.system_name] = var.data_type_id

    # Apply pivots
    if export_datasheet_request.pivot_details:
        logger.info(
            "Applying pivot on datasheet_id: {} with pivot details: {}".format(
                datasheet_id, export_datasheet_request.pivot_details.model_dump()
            )
        )
        variables = DatasheetVariableSelector(
            client_id, datasheet_id
        ).get_selected_variables_with_permission(logged_in_user)

        total_column_count = data[
            export_datasheet_request.pivot_details.columns[0]
        ].nunique() * len(export_datasheet_request.pivot_details.aggfunc)

        if total_column_count > MAX_COLUMN_LIMIT:
            raise DatasheetException(
                code="DATA_PIVOT_MEMORY_ERROR",
                message=f"Cannot apply pivot on a datasheet with more than {MAX_COLUMN_LIMIT} columns",
            )

        var_datatype_map = {var.system_name: var.data_type_id for var in variables}

        data = pivot_and_sort_data(
            data, export_datasheet_request.pivot_details.model_dump(), var_datatype_map
        )
        data.reset_index(inplace=True)
        logger.info("Pivot applied on datasheet_id: {}".format(datasheet_id))

    else:
        date_id = VariableDataTypeAccessor().get_data_type("Date").id
        for key, val in var_datatype_map.items():
            if val == date_id:
                # remove milliseconds from datetime
                data[key] = pd.to_datetime(data[key]).dt.strftime("%Y-%m-%d %H:%M:%S")

    # Rename the columns to display names
    if export_datasheet_request.pivot_details:
        data = rename_pivot_columns(
            data, export_datasheet_request.pivot_details.aggfunc, var_type_map
        )
    else:
        data.rename(columns=var_type_map, inplace=True)  # type: ignore

    csv_file_name = "{}.csv".format(data_source.name)
    # Fix date format to DD MMM YYYY
    datasheet_as_csv = data.to_csv(index=False, date_format="%d %b %Y")

    analytics_data = {
        "user_id": updated_by,
        "event_name": SegmentEvents.EXPORT_DATASHEET.value,
        "event_properties": {
            SegmentProperties.DATABOOK_NAME.value: db_name,
            SegmentProperties.DATABOOK_ID.value: databook_id,
            SegmentProperties.DATASHEET_NAME.value: ds_name,
            SegmentProperties.DATA_OUTPUT.value: data_output,
        },
    }
    analytics = CoreAnalytics(analyser_type="segment")
    analytics.send_analytics(analytics_data)
    logger.info(
        "Export successful for datasheet_id: {} as: {}".format(
            datasheet_id, csv_file_name
        )
    )
    # TODO: Add a response type: Figure out the type of datasheet_as_csv (df.to_csv)
    return datasheet_as_csv, csv_file_name


def datasheet_source_options(*, client_id, datasheet_id) -> DatasheetSourceOptions:
    return DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    ).get_datasheet_source_options()


@log_time_taken()
def update_datasheets_with_additional_source_variables(
    client_id, datasheet_ids: list[str], knowledge_date=None
):
    """
    Update the datasheets and their variables if source sheets have additional primary key variables.

    Scenario:
    - Source sheet got additional primary key variables by adding column in group_by transformation.
    - When we generate any of the child datasheet, child sheet will not have these primary key variables.
        Unless -> The child datasheet is modified and saved after the source sheet is modified. (we can do this by validate and save in datsheet UI)
    - This function will update the child datasheet and its variables to have these primary key variables. (if any)

    Example:
        Sheet_A -> Sheet_B -> Sheet_C[join, join]
        Sheet_A is modified to have additional primary key variables.
        Sheet_B, Sheet_C[join, join] will not have these primary key variables persisted in database.
        This function will update Sheet_B, Sheet_C to have these primary key variables.
            What it updates:
                - datasheet -> is_config_changed, is_pk_modified, transformation_spec, primary_key, ordered_columns
                - datasheet_variable (add new variables)

    Args:
        client_id: The client identifier.
        datasheet_ids: A list of datasheet IDs to update. (ordered list of datasheet ids)
        knowledge_date: The date of knowledge for the update. Defaults to current time if not provided.
    """
    if not knowledge_date:
        knowledge_date = timezone.now()

    for datasheet_id in datasheet_ids:
        # Get the datasheet record and variables
        datasheet_selector = DatasheetSelector(
            client_id=client_id, datasheet_id=datasheet_id
        )
        datasheet_variables_selector = DatasheetVariableSelector(
            client_id=client_id, datasheet_id=datasheet_id
        )

        # Get the datasheet record and variables
        datasheet_record = datasheet_selector.get_datasheet(as_dict=True)
        datasheet_variables = datasheet_variables_selector.get_variables()

        # Get the additional source variables and modified transformation spec
        additional_source_variables = (
            datasheet_selector.get_additional_source_variables(
                datasheet_record, datasheet_variables
            )
        )
        additional_source_variables = DatasheetVariablesList(
            additional_source_variables
        ).model_dump(by_alias=True)

        transformation_spec = TransformationsListModel(
            datasheet_record["transformation_spec"]
        ).model_dump(by_alias=True)

        if transformation_spec:
            # If transformation spec is present, then output_columns of the modified spec will have the ordered columns
            last_transformation = transformation_spec[-1]
            primary_key = [
                var["system_name"]
                for var in last_transformation["output_columns"]
                if var["is_primary"]
            ]
            ordered_columns = [
                var["system_name"] for var in last_transformation["output_columns"]
            ]
        else:
            primary_key = datasheet_record["primary_key"] + [
                var["system_name"]
                for var in additional_source_variables
                if var.get("is_primary", False)
            ]
            ordered_columns = datasheet_record["ordered_columns"] + [
                var["system_name"] for var in additional_source_variables
            ]

        # primary_key, ordered_columns duplicate check : remove duplicate keys (if any) while preserving the order
        primary_key = list(dict.fromkeys(primary_key))
        ordered_columns = list(dict.fromkeys(ordered_columns))
        # If any primary key present in additional source variables, then update the datasheet record and variables
        if any([var.get("is_primary", False) for var in additional_source_variables]):
            logger.info("Updating sheet - %s", datasheet_id)
            logger.info(
                "Variables to be added: %s",
                [var["system_name"] for var in additional_source_variables],
            )

            datasheet_selector.bitemporal_update(
                record_identifier=datasheet_record["temporal_id"],
                data={
                    "is_config_changed": True,
                    "is_pk_modified": True,
                    "transformation_spec": transformation_spec,
                    "primary_key": primary_key,
                    "ordered_columns": ordered_columns,
                },
                invalidation_date=knowledge_date,
            )

            # Construct the primary key variables
            primary_key_variables = [
                var
                | {
                    "client_id": client_id,
                    "datasheet_id": datasheet_id,
                    "databook_id": datasheet_record["databook_id"],
                    "knowledge_begin_date": knowledge_date,
                }
                for var in additional_source_variables
                if var.get("is_primary", False)
            ]

            # Create the primary key variables
            DatasheetVariableSelector(
                client_id=client_id, datasheet_id=datasheet_id
            ).create_ds_variables(primary_key_variables)

            # Update the datasheet version writer
            DatasheetVersionWriter(
                client_id=client_id, datasheet_id=datasheet_id, version="v1"
            ).transform_and_save()


@transaction.atomic
def refresh_datasheet(
    datasheet_generate_model: DatasheetGenerateRequest,
    audit: dict,
) -> DatasheetGenerateResponse | DatasheetGenerateFailureResponse:
    client_id = datasheet_generate_model.client_id
    datasheet_id = str(datasheet_generate_model.datasheet_id)
    is_force_invalidate = datasheet_generate_model.is_force_invalidate
    is_report_data_stale = datasheet_generate_model.is_report_data_stale

    e2e_sync_run_id = uuid.uuid4()
    report_kd = timezone.now()
    log_context = {
        "client_id": client_id,
        "datasheet_id": datasheet_id,
        "e2e_sync_run_id": e2e_sync_run_id,
    }
    set_threadlocal_context(log_context)

    # when the global sync run is disabled, do not submit the sync request to queue
    if not EtlGlobalSyncStatusService.is_global_sync_enabled_for_client(client_id):
        logger.info("GLOBAL SYNC NOT SET")
        return DatasheetGenerateFailureResponse(
            code="global_sync_false",
            status=status.HTTP_403_FORBIDDEN,
            message="We're updating Everstage to improve your experience, so sync is paused for now. You can try again in about an hour.",
        )

    if etl_sync_status_service.is_end_to_end_sync_running(
        client_id
    ) or etl_sync_status_service.is_upstream_sync_running(client_id):
        return DatasheetGenerateFailureResponse(
            code="e2e_running",
            status=status.HTTP_400_BAD_REQUEST,
            message="Connector sync is in progress in the background. Please try later.",
        )

    # Check if the current datasheet is already being refreshed
    locked_datasheets = current_all_locked_datasheets(
        client_id=client_id,
        task=[
            SYNC_OBJECT.DATASHEET_SYNC.value,
            SYNC_OBJECT.DATABOOK_SYNC.value,
        ],
    )
    if UUID(datasheet_id) in locked_datasheets:
        return DatasheetGenerateFailureResponse(
            code="datasheet_already_refreshing",
            status=status.HTTP_409_CONFLICT,
            message="This datasheet is already being refreshed. Please wait for the current refresh to complete.",
        )

    subscription_plan = get_client_subscription_plan(client_id=client_id)
    report_queue_name = get_queue_name_respect_to_task_group(
        client_id=client_id,
        subscription_plan=subscription_plan,
        task_group=TaskGroupEnum.REPORT.value,
    )
    databook_queue_name = get_queue_name_respect_to_task_group(
        client_id=client_id,
        subscription_plan=subscription_plan,
        task_group=TaskGroupEnum.DATABOOK.value,
    )
    report_tasks = []
    is_report_etl_running = False
    # if is_report_data_stale stale -> passed from is_datasheet_data_stale request result
    if is_report_data_stale:
        task = SYNC_OBJECT.REPORT_WRAPPER_SYNC.value
        is_report_etl_running = ETLLockAccessor(
            client_id=client_id, e2e_sync_run_id=None, sync_run_id=None
        ).is_lock_active(tasks=[task])
        if not is_report_etl_running:
            logger.info("BEGIN: REPORT ETL -- datasheet update action")

            datasheet_graph = DataSheetGraph(
                client_id=client_id,
                include_stale_information_query=True,
            )
            stale_report_object_ids: set = datasheet_graph.associated_report_objects(
                datasheet_id=str(datasheet_id),
                fetch_only_stale_objects=True,
            )
            if stale_report_object_ids:
                report_tasks.extend(
                    report_etl.populate_report_etl_tasks(
                        client_id=client_id,
                        e2e_sync_run_id=e2e_sync_run_id,
                        sync_run_id=None,
                        report_object_list=stale_report_object_ids,
                        report_queue_name=report_queue_name,
                        sync_mode="changes",
                        knowledge_date=report_kd,
                        log_context=None,
                    )
                )
            logger.info("END: REPORT ETL -- datasheet update action")

    # async version -> execute group_tasks here
    # syncrhonously send response to frontend(sync added to engine)
    etl_status_params = {"datasheet_id": datasheet_id}

    etl_sync_status_service.insert_etl_sync_status(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        task=ETL_ACTIVITY.GENERATE_DATASHEET.value,
        sync_status=ETL_STATUS.STARTED.value,
        sync_start_time=timezone.now(),
        audit=audit,
        params=etl_status_params,
        additional_info={"is_triggered_from_datasheet_ui": True},
    )

    # chain the report etl tasks and the datasheet sync task
    # and delay the execution (add it to queue so that engine can pick it up)
    datasheet_task = get_datasheet_wrapper_sync_task(
        client_id=client_id,
        e2e_sync_run_id=e2e_sync_run_id,
        sync_type=None,  # type: ignore
        datasheet_id=datasheet_id,
        is_force_invalidate=is_force_invalidate,
    )

    chain(
        *report_tasks,
        datasheet_task,
        etl_sync_status_service.update_completion_time.si(
            client_id=client_id,
            e2e_sync_run_id=e2e_sync_run_id,
        ).set(queue=databook_queue_name),
    ).apply_async()
    # For both serial and parallel_tree datasheet processing strategy
    # We are updating the completion time in the above celery chain task.

    if is_report_etl_running:
        return DatasheetGenerateFailureResponse(
            code="report_locked",
            status=status.HTTP_429_TOO_MANY_REQUESTS,
            message="Report Update triggered by another process. Please wait or try again later to see the update data",
        )

    response = {
        "task_info": {
            "tasks": {"report_etl": len(report_tasks), "datasheet_etl": 1},
            "status": "Task created successfully",
            "e2e_sync_run_id": e2e_sync_run_id,
        },
        "status_code": status.HTTP_201_CREATED,
    }

    return DatasheetGenerateResponse(**response)


def validate_datasheet_before_update(
    *, client_id, datasheet_id, databook_id, source_id, source_type
):
    """
    Validate datasheet before update
    """
    logger.info(
        f"Validating datasheet before update for client_id: {client_id}, datasheet_id: {datasheet_id}, databook_id: {databook_id}, source_id: {source_id}, source_type: {source_type}"
    )
    variables = DatasheetVariablesList(
        DatasheetVariableTemp.objects.filter(
            client_id=client_id, datasheet_id=datasheet_id
        )
    ).model_dump(by_alias=True)

    if not variables:
        raise DatasheetException(
            code="STALE_UPDATE_DETECTED",
            message="Current update is stale, please refresh the page and try again",
        )

    transformations_objs = (
        ds_selectors.DatasheetTransformationSelector(
            client_id=client_id, datasheet_id=datasheet_id
        )
        .get_transformations()
        .order_by("order")
    )
    transformations = [
        transformation.spec for transformation in (transformations_objs or [])
    ]

    source_variables = datasheet_fetch_source_variables(
        client_id=client_id,
        source_type=source_type,
        source_id=source_id,
        only_selected=True,
    )

    validate_request = DatasheetValidateRequest(
        client_id=client_id,
        datasheet_id=datasheet_id,
        databook_id=databook_id,
        variables=variables,
        transformations=transformations,
        source_id=source_id,
        source_type=source_type,
        source_variables=source_variables,
    )

    validate_datasheet(validate_request)
    logger.info(
        f"Validation successful for client_id: {client_id}, datasheet_id: {datasheet_id}, databook_id: {databook_id}, source_id: {source_id}, source_type: {source_type}"
    )


@transaction.atomic
def datasheet_update(*, client_id, datasheet_id, data):
    """
    Update datasheet
    """
    # Check if any datasheet or databook is in locked state. If yes, then we allow to edit that databook/datasheet
    # TODO: @sukanya - 30/09/2024 - Extract this logic as a function and replace all usages.
    locked_datasheets = current_all_locked_datasheets(
        client_id=client_id,
        task=[
            SYNC_OBJECT.DATASHEET_SYNC.value,
            SYNC_OBJECT.DATABOOK_SYNC.value,
        ],
    )
    if UUID(datasheet_id) in locked_datasheets:
        reason = _construct_reason_for_generation_in_progress_delete_error(
            client_id=client_id,
            datasheet_ids=list(locked_datasheets),
        )
        # Checking if the generation is in progress, If yes, then return the reason for the failure
        raise DatasheetException(
            code="DATASHEET_GENERATION_IN_PROGRESS",
            message=reason,
        )

    # audit logs and segment analytics
    # compute datasheet order if source is changed
    datasheet_selector = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )

    if should_revalidate_datasheet_on_update(client_id):
        datasheet = datasheet_selector.get_datasheet(as_dict=True)
        databook_id = datasheet.get("databook_id")
        source_id = datasheet.get("source_id")
        source_type = datasheet.get("source_type")

        validate_data = {
            "client_id": client_id,
            "datasheet_id": datasheet_id,
            "databook_id": databook_id,
            "source_id": source_id,
            "source_type": source_type,
        }

        validate_datasheet_before_update(**validate_data)

    datasheet_selector.update_datasheet(data)
    compute_datasheet_order(client_id, datasheet_id)
    # Update the v1 datasheet transform spec if the config is changed
    if data.get("is_config_changed", False):
        DatasheetVersionWriter(
            client_id=client_id, datasheet_id=datasheet_id, version="v1"
        ).transform_and_save()

    try:
        logger.info("Checking if should insert meta data to vector db")
        if should_insert_meta_data_to_vec_db(client_id):
            logger.info(
                f"Updating datasheet metadata in vector db for client {client_id} and datasheet {datasheet_id}"
            )
            databook_id = datasheet_selector.get_datasheet().databook_id
            upsert_ds_meta_data_in_vector_db(
                client_id,
                databook_id,
                datasheet_id,
                add_stats=False,
            )
    except Exception as e:
        logger.info(
            f"Failed to update datasheet metadata in vector db for client {client_id} and datasheet {datasheet_id}: {str(e)}"
        )


@transaction.atomic
def validate_datasheet(
    request_model: DatasheetValidateRequest,
) -> DatasheetValidateResponse:
    """
    Validate datasheet
    """
    if any(
        isinstance(transformation, EmptyTransformationModel)
        for transformation in request_model.transformations
    ):
        raise DatasheetException(
            code="INVALID_TRANSFORMATION",
            message="Some of transformation are not valid, Fill the transformations before validate",
        )

    if not request_model.source_id:
        raise DatasheetException(
            code="INVALID_SOURCE_ID",
            message="Please select a source for datasheet to proceed",
        )

    # Check if any datasheet or databook is in locked state. If yes, then we dont allow to delete that databook/datasheet
    locked_datasheets = current_all_locked_datasheets(
        client_id=request_model.client_id,
        databook_ids=request_model.databook_id,
        task=[
            SYNC_OBJECT.DATASHEET_SYNC.value,
            SYNC_OBJECT.DATABOOK_SYNC.value,
        ],
    )
    if request_model.datasheet_id in locked_datasheets:
        reason = _construct_reason_for_generation_in_progress_delete_error(
            client_id=request_model.client_id, datasheet_ids=list(locked_datasheets)
        )
        # Checking if the generation is in progress, If yes, then return the reason for the failure
        raise DatasheetException(
            code="DATASHEET_GENERATION_IN_PROGRESS",
            message=reason,
        )

    # This line should not be moved inside the try-except block as it can cause issues with transaction management.
    # If the transaction fails and an integrity error is thrown when we try to get the localized message for the code,
    # since the transaction is already broken, TransactionManagementError is thrown.
    concurrent_validation_error_message = get_localized_message_utils(
        "CONCURRENT_VALIDATION_ERROR",
        str(request_model.client_id),
    )
    try:
        validate_request_details = request_model.model_dump(by_alias=True)
        ds_validator = DatasheetValidator(
            client_id=validate_request_details["client_id"],
            datasheet_id=validate_request_details["datasheet_id"],
            variables=validate_request_details["variables"],
            transformations=validate_request_details["transformations"],
            source_variables=validate_request_details["source_variables"],
            source_id=validate_request_details["source_id"],
            source_type=validate_request_details["source_type"],
            has_source_changed=validate_request_details["has_source_changed"],
        )
        if validate_request_details["initial_validation"]:
            # add additional primary key variables to the source variables
            additional_source_variables = (
                validate_request_details["additional_source_variables"] or []
            )
            additional_primary_key_variables = [
                variable
                for variable in additional_source_variables
                if variable["is_primary"]
            ]
            # populate temp tables from source during initial validation
            ds_validator.populate_temp_tables_from_source(
                additional_variables=additional_primary_key_variables
            )
        ds_validator.validate()
        ds_validator.populate_validated_details()
        has_config_changed = ds_validator.has_config_changed()
        details = ds_validator.get_draft_details()
        if validate_request_details["has_source_changed"]:
            # Remove the transformations from temp table since source is changed and transformations are not valid
            # However the invalid state will be sent to UI for user to fix the transformations
            ds_validator.delete_transformations_from_temp()
        return DatasheetValidateResponse(
            datasheet_id=request_model.datasheet_id,
            variables=details["variables"],
            source_variables=details["source_variables"],
            transformations=[
                transformation["spec"] for transformation in details["transformations"]
            ],
            has_config_changed=has_config_changed,
            source_type=validate_request_details["source_type"],
            source_id=validate_request_details["source_id"],
        )
    except IntegrityError as e:
        logger.warning(
            f"Concurrent validation detected for datasheet {request_model.datasheet_id}: {str(e)}"
        )
        raise DatasheetException(
            code="CONCURRENT_VALIDATION_ERROR",
            message=concurrent_validation_error_message,
            status=status.HTTP_409_CONFLICT,
        ) from e


# Cache the source name of a variable only for a minute
# So that repeated db calls will not made for same source id and source type
@cached(cache=TTLCache(ttl=10, maxsize=1024))
def fetch_source_name_of_variable(
    *, client_id: int, source_id: str, source_type: str
) -> str:
    """
    Fetch source name of a variable
    """
    if source_type == DatasheetSourceType.CUSTOM_OBJECT.value:
        return get_custom_object_by_id(
            client_id=client_id, custom_object_id=int(source_id)
        ).name
    if source_type == DatasheetSourceType.REPORT_OBJECT.value:
        return get_ever_object_by_id(ever_object_id=source_id).name
    if source_type == DatasheetSourceType.DATASHEET.value:
        return (
            DatasheetSelector(client_id=client_id, datasheet_id=UUID(source_id))
            .get_datasheet()
            .name
        )
    else:
        raise ValueError(f"Invalid source type {source_type}")


def fetch_source_display_name_history(
    *,
    client_id: int,
    source_id: str,
    source_type: str,
    source_variable_id: str | None,
    display_name: str,
) -> str:
    """
    Fetch source display name history of a variable
    Example:

    custom_object_name: object
    variable display name = "name"

    First Level:

    sheet name: d1
    variable display name: "re name"
    source name: "object"
    output: "name << object"

    Second Level:

    sheet name: d2
    variable name: "re re name"
    source name: "d1"
    output: "re name << d1 << object"

    Third Level:

    sheet name: d3
    variable name: "name"
    source name: "d2"
    output: "re re name << d2 << d1 << object"

    """
    # For Calculated fields, source_variable_id will be None
    if not source_variable_id:
        source_name = fetch_source_name_of_variable(
            client_id=client_id, source_id=source_id, source_type=source_type
        )
        return f"{display_name} << {source_name}"

    # Remove the __ss__0, __ss__1, etc from the source variable id
    if SAME_SOURCE_VAR in source_variable_id:
        source_variable_id = source_variable_id.split(SAME_SOURCE_VAR)[0]

    source_name = fetch_source_name_of_variable(
        client_id=client_id, source_id=source_id, source_type=source_type
    )
    variable_name = ""
    if source_type == DatasheetSourceType.CUSTOM_OBJECT.value:
        variable_name = get_custom_object_variable_display_name(
            client_id=client_id,
            custom_object_id=int(source_id),
            variable_id=source_variable_id,
        )
    elif source_type == DatasheetSourceType.REPORT_OBJECT.value:
        variable_name = get_report_object_variable_display_name(
            client_id=client_id,
            ever_object_id=source_id,
            variable_id=source_variable_id,
        )
    elif source_type == DatasheetSourceType.DATASHEET.value:
        variable = DatasheetVariableSelector(
            client_id=client_id, datasheet_id=UUID(source_id)
        ).get_variable(variable_id=source_variable_id)

        variable_name = variable.display_name

        derived_source_name = _fetch_source_name_history_utils(
            client_id=client_id,
            source_id=variable.source_id,
            source_type=variable.source_type,
            source_variable_id=variable.source_variable_id,
        )

        return (
            f"{variable_name} << {source_name} << {derived_source_name}"
            if derived_source_name
            else f"{variable_name} << {source_name}"
        )

    else:
        raise ValueError(f"Invalid source type {source_type}")

    return f"{variable_name} << {source_name}"


@cached(cache=TTLCache(ttl=60, maxsize=1024))
def _fetch_source_name_history_utils(
    *, client_id, source_id, source_type, source_variable_id
):
    """
    Utility function to fetch source name history of a variable
    """
    source_name = fetch_source_name_of_variable(
        client_id=client_id, source_id=source_id, source_type=source_type
    )

    if source_type != DatasheetSourceType.DATASHEET.value:
        return source_name

    # Source variable id will be none if it is calculated field
    if not source_variable_id:
        return ""

    ## Remove the __ss__0, __ss__1, etc from the source variable id
    if SAME_SOURCE_VAR in source_variable_id:
        source_variable_id = source_variable_id.split(SAME_SOURCE_VAR)[0]

    variable = DatasheetVariableSelector(
        client_id=client_id, datasheet_id=UUID(source_id)
    ).get_variable(variable_id=source_variable_id)

    derived_name = _fetch_source_name_history_utils(
        client_id=client_id,
        source_id=variable.source_id,
        source_type=variable.source_type,
        source_variable_id=variable.source_variable_id,
    )

    if derived_name:
        return f"{source_name} << {derived_name}"

    return source_name


def fetch_all_ds_filter_operators() -> DsFilterOperatorsList:
    """
    Fetch all filter operators for datasheet
    """
    return DsFilterOperatorsList(DsFilterOperatorSelector().list_all())


@transaction.atomic
def delete_datasheet(  # noqa: PLR0915 # TODO: Split this function into smaller functions
    delete_datasheet_data,
) -> DeleteDatasheetResponse:
    client_id = delete_datasheet_data["client"]
    datasheet_id = UUID(str(delete_datasheet_data["datasheet_id"]))
    user_email = delete_datasheet_data["created_by"]

    datasheet_selector = DatasheetSelector(
        client_id=client_id, datasheet_id=datasheet_id
    )
    datasheet = datasheet_selector.get_datasheet(as_dict=True)
    datasheet_name = datasheet["name"]
    databook_id = datasheet["databook_id"]
    datasheet.get("additional_details", {}).update(
        delete_datasheet_data["additional_details"]
    )
    additional_details = datasheet.get("additional_details", {})

    # Check if any datasheet or databook is in locked state. If yes, then we dont allow to delete that databook/datasheet
    locked_datasheets = current_all_locked_datasheets(
        client_id=client_id,
        databook_ids=databook_id,
        task=[
            SYNC_OBJECT.DATASHEET_SYNC.value,
            SYNC_OBJECT.DATABOOK_SYNC.value,
        ],
    )
    if datasheet_id in locked_datasheets:
        reason = _construct_reason_for_generation_in_progress_delete_error(
            client_id=client_id, datasheet_ids=list(locked_datasheets)
        )
        # Checking if the generation is in progress, If yes, then return the reason for the failure
        raise DatasheetException(
            code="DATASHEET_GENERATION_IN_PROGRESS",
            message=reason,
        )

    knowledge_date = timezone.now()
    co_permission_for_datasheet = (
        does_user_has_permission_to_custom_objects_in_datasheet(
            client_id, databook_id, datasheet_id, user_email, knowledge_date=None
        )
    )

    if not co_permission_for_datasheet:
        logger.error(
            "Cannot delete datasheet since you don't have access to the underlying data"
        )
        raise DatasheetException(
            code="NO_ACCESS_TO_UNDERLYING_DATA",
            message="Cannot delete datasheet since you don't have access to the underlying data",
        )

    if check_if_ds_used_in_analytics(client_id=client_id, datasheet_id=datasheet_id):
        raise DatasheetException(
            code="DATASHEET_USED_IN_ANALYTICS",
            message=f"Cannot delete datasheet {datasheet_name} since its used in Analytics",
        )

    dependency_details = get_datasheet_dependencies_details(
        client_id=client_id, datasheet_id=str(datasheet_id)
    )
    if dependency_details["has_dependent"]:
        raise DatasheetException(
            code="DEPENDENT_DATASHEETS_FOUND",
            message=f"Cannot delete datasheet {datasheet_name} since the following datasheets are dependent on this datasheet - {dependency_details['dependent_details']}",
        )

    datasheet_accessor = DatasheetSelector(client_id=client_id)
    ds_list = datasheet_accessor.get_datasheets_for_databooks([databook_id])
    ds_id_map = {}
    for ds in ds_list:
        ds_id_map[str(ds.datasheet_id)] = ds

    datasheet = ds_id_map[str(datasheet_id)]

    if is_datasheets_used_in_any_commission_plan(
        client_id=client_id,
        datasheet_ids=[datasheet_id],
        commission_type=COMMISSION_TYPE.FORECAST,
    ):
        raise DatasheetException(
            code="DATASHEET_USED_IN_FORECAST_PLAN",
            message=f"Cannot delete datasheet {datasheet_name} since the datasheet is used forecast plan",
        )

    if is_datasheets_used_in_any_commission_plan(
        client_id=client_id, datasheet_ids=[datasheet_id]
    ) or (
        is_datasheets_used_in_any_settlement_rule(
            client_id=client_id, datasheet_ids=[datasheet_id]
        )
    ):
        raise DatasheetException(
            code="DATASHEET_USED_IN_SETTLEMENT_RULE_OR_COMMISSION_PLAN",
            message=f"Cannot delete datasheet {datasheet_name} as it is used in commission plan or settlement rule",
        )

    if CrystalAdminAccessor(client_id).is_datasheet_used_in_crystal_view(
        str(datasheet_id)
    ):
        raise DatasheetException(
            code="DATASHEET_USED_IN_CRYSTAL_VIEW",
            message=f"Cannot delete datasheet {datasheet_name} since the datasheet is used in crystal view",
        )

    databook_datasheet_part_of_workflow: bool = (
        is_databook_datasheet_part_of_any_workflow(
            client_id=client_id,
            databook_id=str(databook_id),
            datasheet_id=str(datasheet_id),
        )
    )

    if databook_datasheet_part_of_workflow:
        raise DatasheetException(
            code="DATASHEET_USED_IN_WORKFLOW",
            message=f"Cannot delete datasheet {datasheet_name} as it is used in workflow",
        )

    if HrisConfigAccessor(client_id).is_datasheet_used_in_hris(datasheet_id):
        raise DatasheetException(
            code="DATASHEET_USED_IN_HRIS_INTEGRATION",
            message=f"Cannot delete datasheet {datasheet_name} since the datasheet is used in HRIS integration",
        )

    try:
        ###################### audit log #####################
        event_type_code = EVENT["DELETE_DATASHEET"]["code"]
        event_key = datasheet_id
        summary = datasheet.name if datasheet else None
        audit_data = {}
        updated_by = additional_details.get("updated_by")
        updated_at = knowledge_date
        ######################################################

        # Delete datasheet permissions and targets
        invalidate_datasheet_permissions(
            client_id, str(databook_id), str(datasheet_id), knowledge_date
        )

        (
            source_type,
            source_name,
            datasheet_name,
        ) = datasheet_source_details(client_id, databook_id, datasheet_id)

        DatasheetVariableSelector(
            client_id, datasheet_id=datasheet_id
        ).get_variables().update(knowledge_end_date=knowledge_date)

        client_subscription_plan = get_client_subscription_plan(client_id=client_id)
        invalidation_task_queue = get_queue_name_respect_to_task_group(
            client_id=client_id,
            subscription_plan=client_subscription_plan,
            task_group=TaskGroupEnum.MISC.value,
        )
        # soft delete all records in datasheet_data when sheet is deleted asynchronously
        invalidate_data_datasheet.si(
            client_id=client_id,
            databook_id=str(databook_id),
            datasheet_id=str(datasheet_id),
            knowledge_date=knowledge_date,
        ).set(queue=invalidation_task_queue).apply_async()

        # drop all intermediate result tables
        delete_transformation_result_tables(
            client_id=client_id,
            datasheet_id=str(datasheet_id),
            knowledge_date=knowledge_date,
        )
        invalidation_date = timezone.now()

        datasheet_selector.delete_datasheet(invalidation_date=invalidation_date)

        # updating the kbd of data book
        record_databook_changes_on_delete(
            client_id,
            str(databook_id),
            str(datasheet_id),
            knowledge_date,
            additional_details,
            logger,
        )
        try:
            logger.info("Checking if should insert meta data to vector db")
            if should_insert_meta_data_to_vec_db(client_id):
                logger.info(
                    f"Deleting datasheet metadata from vector db for client {client_id} and datasheet {datasheet_id}"
                )
                delete_ds_meta_data_from_vector_db(client_id, [datasheet_id])
        except Exception:
            logger.info(
                f"Failed to delete datasheet metadata from vector db for client {client_id} and datasheet {datasheet_id}"
            )

        audit_services.log(
            client_id,
            event_type_code,
            event_key,
            summary,
            updated_by,
            updated_at,
            audit_data,
        )

        analytics_data = {
            "user_id": additional_details["updated_by"],
            "event_name": SegmentEvents.DELETE_DATASHEET.value,
            "event_properties": {
                SegmentProperties.DATABOOK_ID.value: str(databook_id),
                SegmentProperties.DATASHEET_NAME.value: str(datasheet_name),
                SegmentProperties.DATASHEET_DATA_SOURCE.value: str(source_type),
                SegmentProperties.DATASHEET_SOURCE_NAME.value: str(source_name),
            },
        }
        analytics = CoreAnalytics(analyser_type="segment")
        analytics.send_analytics(analytics_data)
        return DeleteDatasheetResponse(
            status="SUCCESS",
            datasheet_id=datasheet_id,
            databook_id=databook_id,
            datasheet_name=datasheet_name,
        )
    except Exception as error:
        print_exc()
        raise DatasheetException(
            code="UNKOWN_ERROR",
            message=f"Unknown error occured while deleting datasheet {datasheet_name}, Please try again later or contact admin",
        ) from error


def compute_datasheet_origin(
    *, client_id: int, source_data_origin, transformations, variables
) -> str:
    data_origin = set()
    data_origin.add(source_data_origin)

    data_origin.update(
        _compute_data_origin_from_transformations(
            client_id=client_id, transformations=transformations
        )
    )

    # Consider variables for evaluating datasheet data origin
    data_origin.update(
        _compute_data_origin_from_variables(client_id=client_id, variables=variables)
    )

    # Data origin priority in datasheet: commission object > inter_obj > system object > custom object

    data_origin_orders = [
        DataOrigin.COMMISSION_OBJECT.value,
        DataOrigin.INTER_OBJECT.value,
        DataOrigin.SYSTEM_OBJECT.value,
        DataOrigin.CUSTOM_OBJECT.value,
        DataOrigin.FORECAST_OBJECT.value,
        DataOrigin.INTER_FORECAST_OBJECT.value,
    ]

    for data_origin_order in data_origin_orders:
        if data_origin_order in data_origin:
            return data_origin_order

    raise ValueError("Invalid data origin")


def get_source_data_origin(client_id, source_id, source_type):
    if source_type == DatasheetSourceType.CUSTOM_OBJECT.value:
        return DataOrigin.CUSTOM_OBJECT.value
    if source_type == DatasheetSourceType.REPORT_OBJECT.value:
        return get_ever_object_by_id(ever_object_id=source_id).data_origin
    if source_type == DatasheetSourceType.DATASHEET.value:
        return (
            DatasheetSelector(client_id=client_id, datasheet_id=source_id)
            .get_datasheet()
            .data_origin
        )

    raise ValueError("Invalid source type")


@transaction.atomic
def flush_draft_details(client_id: int, datasheet_id: str | UUID) -> None:
    DatasheetVariableTemp.objects.filter(
        client_id=client_id, datasheet_id=datasheet_id
    ).delete()

    ds_selectors.DatasheetTransformationSelector(
        client_id=client_id, datasheet_id=datasheet_id
    ).get_transformations().delete()


def _compute_data_origin_from_transformations(client_id, transformations):
    filtered_transformations = list(
        filter(
            lambda transformation: transformation["type"]
            in [
                TransformationType.UNION.value,
                TransformationType.JOIN.value,
                TransformationType.TEMPORAL_SPLICE.value,
                TransformationType.GET_USER_PROPERTIES.value,
            ],
            transformations,
        )
    )
    if not filtered_transformations:
        return set()

    data_origin = set()

    for transformation in filtered_transformations:
        if transformation["type"] in [
            TransformationType.UNION.value,
            TransformationType.JOIN.value,
        ]:
            data_origin.add(
                get_source_data_origin(
                    client_id=client_id,
                    source_id=transformation["with"],
                    source_type=DatasheetSourceType.DATASHEET.value,
                )
            )
        elif transformation["type"] == TransformationType.GET_USER_PROPERTIES.value:
            data_origin.add(DataOrigin.SYSTEM_OBJECT.value)
        elif transformation["type"] == TransformationType.TEMPORAL_SPLICE.value:
            # Initial data origin is the source datasheet so we can skip the first element
            for datasource in transformation["meta"][1:]:
                data_origin.add(
                    get_source_data_origin(
                        client_id=client_id,
                        source_id=datasource["source_id"],
                        source_type=datasource["source_type"],
                    )
                )
    return data_origin


def _compute_data_origin_from_variables(*, client_id: int, variables: list[dict]):
    hierarchy_variables = list(
        filter(lambda variable: is_hierarchy_calculated_field(variable), variables)
    )
    data_origin = set()

    for variable in hierarchy_variables:
        meta_data = pydash.get(variable, "meta_data.hierarchy", {})
        if (
            meta_data[HierarchyUtils.REFERENCE_SHEET_KEY.value]
            != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value
        ):
            data_origin.add(
                get_source_data_origin(
                    client_id=client_id,
                    source_id=meta_data[HierarchyUtils.REFERENCE_SHEET_KEY.value],
                    source_type=DatasheetSourceType.DATASHEET.value,
                )
            )
    return data_origin


def _find_connected_nodes_for_datasheet(current_id, edges):
    """
    Find the path from the current datasheet to all root datasheets in the graph,
    and all descendants of the current datasheet.

    Args:
        current_id (str): The ID of the current datasheet.
        edges (list): A list of dictionaries representing the edges in the graph.
                      Each dictionary should have 'from' and 'to' keys.

    Returns:
        set: A set of datasheet IDs representing the paths from the current datasheet
             to all roots, including both the current and root datasheets, and all descendants.

    Example:
        >>> edges = [{'from': 'A', 'to': 'C'}, {'from': 'B', 'to': 'C'}, {'from': 'C', 'to': 'D'}]
        >>> _find_connected_nodes_for_datasheet('D', edges)
        {'D', 'C', 'A', 'B'}
    """
    connected_nodes = set([current_id])

    # Find all descendants
    to_visit = [current_id]
    visited = set()
    while to_visit:
        current = to_visit.pop()
        if current not in visited:
            visited.add(current)
            children = [edge["to"] for edge in edges if edge["from"] == current]
            connected_nodes.update(children)
            to_visit.extend(children)

    # Find paths to all roots
    to_visit = [current_id]
    visited = set()
    while to_visit:
        current = to_visit.pop()
        if current not in visited:
            visited.add(current)
            parents = [edge["from"] for edge in edges if edge["to"] == current]
            connected_nodes.update(parents)
            to_visit.extend(parents)

    return connected_nodes


def _get_metadata_for_datasheet(client_id, datasheet_id, databook_id, logged_in_user):
    meta_data = {}
    sb_selector = StormBreakerSelector(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
        logged_in_user_email=logged_in_user,
    )
    meta_data["total_records"] = sb_selector.get_records_count()
    meta_data["last_generated_at"] = DbkdPkdMapSelector(
        client_id, datasheet_id
    ).get_datasheet_last_generation_time()
    recent_sync_status = get_recent_datasheet_sync_status(
        client_id=client_id,
        databook_id=databook_id,
        datasheet_id=datasheet_id,
    )
    meta_data["sync_status"] = recent_sync_status.get("sync_status")
    return meta_data


# ruff: noqa: PLR0912


def links_to_edges(links) -> list[dict]:
    return [
        {
            "from": link["source"]["node_id"],
            "to": link["target"]["node_id"],
            "order": link["target"]["order"],
        }
        for link in links
    ]


def create_map_from_links(links, curr_datasheet_id) -> list[dict]:
    link_map = {}

    for link in links:
        source = link["source"]
        target = link["target"]

        if source["node_id"] not in link_map:
            link_map[source["node_id"]] = {"down": [], "up": []}

        if target["node_id"] not in link_map:
            link_map[target["node_id"]] = {"down": [], "up": []}

        if source and "node_id" in source:
            link_map[source["node_id"]]["down"].append(link)
        if target and "node_id" in target:
            link_map[target["node_id"]]["up"].append(link)

    return links_to_edges(
        get_up_connected_links(link_map, curr_datasheet_id, [])
        + get_down_connected_links(link_map, curr_datasheet_id, [])
    )


def get_up_connected_links(link_map, node_id, links) -> list[dict]:
    if link_map[node_id] and len(link_map[node_id]["up"]) > 0 and node_id:
        for link in link_map[node_id]["up"]:
            links.append(link)
            get_up_connected_links(link_map, link["source"]["node_id"], links)
    return links


def get_down_connected_links(link_map, node_id, links) -> list[dict]:
    if link_map[node_id] and len(link_map[node_id]["down"]) > 0 and node_id:
        for link in link_map[node_id]["down"]:
            links.append(link)
            get_down_connected_links(link_map, link["target"]["node_id"], links)
    return links


def get_datasheet_graph_data(
    datasheet_graph_request: DatasheetGraphRequest, logged_in_user: str
) -> dict:
    """
    Returns a response with the graph data for a datasheet.

    Args:
        datasheet_graph_request (DatasheetGraphRequest): The request object containing client_id, datasheet_id.
        logged_in_user (str): The email of the logged-in user.

    Sample output:
        {
            "nodes": [{ id: '08c649bd-36e4-4cc8-bae6-6aff477f00ae', name: 'ds 6' },{ id: '2f4ce533-5b4b-4d3d-a6b8-f229dceac979', name: 'ds4' }],
            "edges": [{
                from: '08c649bd-36e4-4cc8-bae6-6aff477f00ae',
                to: '10623ae2-e1f3-4ab5-820b-bcd879b56d3c',
            },
            {
                from: '2f4ce533-5b4b-4d3d-a6b8-f229dceac979',
                to: '08c649bd-36e4-4cc8-bae6-6aff477f00ae',
            }]
        }
    """
    from spm.services.datasheet_graph.datasheet_graph import DataSheetGraph

    client_id = datasheet_graph_request.client_id
    curr_datasheet_id = str(datasheet_graph_request.datasheet_id)
    is_sheet_level_graph = datasheet_graph_request.is_sheet_level_graph
    curr_databook_id = str(datasheet_graph_request.databook_id)

    ds_graph = DataSheetGraph(client_id=client_id, include_stale_information_query=True)

    # Getting databook_id to databook_names map
    databook_ids = ds_graph.unqiue_databook_ids()
    databook_names_dict = databook_names_by_ids(
        client_id=client_id, databook_ids=list(databook_ids)
    )
    serialized_data = ds_graph.serialize_data()
    ds_info = ds_graph.ds_info

    nodes = []
    edges = []
    source_data_origin = {
        DatasheetSourceType.CUSTOM_OBJECT.value: "Custom Object",
        DatasheetSourceType.REPORT_OBJECT.value: "Report Object",
    }

    node_width = 110
    node_height = 110
    nodes_to_color = []
    connected_node_ids = set()
    allowed_nodes = set()
    # What does 'allowed_nodes' mean?
    # In the UI, we only want to display datasheet nodes
    # from that data book. Additionally, if a datasheet references another data
    # book, we want to show the one-level parent datasheet node as well.
    # This subset of datasheet nodes is referred to as 'allowed_nodes'."

    if is_sheet_level_graph:
        edges = create_map_from_links(serialized_data["links"], curr_datasheet_id)

        edges.sort(key=lambda x: (x["order"] is None, x["order"]))
        # Collect nodes that are directly connected to curr_datasheet_id
        connected_node_ids = {curr_datasheet_id}
        for edge in edges:
            connected_node_ids.add(edge["from"])
            connected_node_ids.add(edge["to"])
    else:
        for edge in serialized_data["links"]:
            if curr_databook_id == ds_info[edge["target"]["node_id"]].databook_id:
                # If the datasheet belongs to the given databook_id
                # then both source and target nodes are allowed
                allowed_nodes.add(edge["source"]["node_id"])
                allowed_nodes.add(edge["target"]["node_id"])
                edge_dict = {
                    "from": edge["source"]["node_id"],
                    "to": edge["target"]["node_id"],
                    "order": edge["target"]["order"],
                }
                edges.append(edge_dict)
        edges.sort(key=lambda x: (x["order"] is None, x["order"]))
        # Find connected nodes (path to root and all descendants)
        if curr_datasheet_id is not None:
            nodes_to_color = (
                _find_connected_nodes_for_datasheet(curr_datasheet_id, edges) or []
            )
        # Update edges with is_colored info
        for edge in edges:
            edge["is_colored"] = (
                edge["from"] in nodes_to_color and edge["to"] in nodes_to_color
            )

    for node in serialized_data["nodes"]:
        node_id = node["node_id"]
        if node_id not in connected_node_ids and node_id not in allowed_nodes:
            continue

        name = node.get("name", node_id)
        is_colored = False
        if is_sheet_level_graph and curr_datasheet_id is not None:
            is_colored = node_id == curr_datasheet_id
        else:
            is_colored = node_id in nodes_to_color
        object_type = node.get("object_type")
        data_origin = node.get("data_origin")
        is_root = node.get("is_root")
        datasheet_staleness = (
            ds_graph.is_datasheet_data_stale(node_id) if not is_root else False
        )
        meta_data = {}

        is_stale = (
            (
                datasheet_staleness["is_report_data_stale"]
                or datasheet_staleness["is_datasheet_stale"]
            )
            if not is_root
            else False
        )

        source_sheet_name = ""
        if "source_id" in node:
            source_sheet = ds_info.get(node["source_id"])
            if source_sheet:
                source_sheet_name = source_sheet.name

        transformation_meta = {}
        hierarchy_reference_datasheet_names = []
        databook_name = ""

        if not is_root:
            transformation_meta = get_transformation_meta_data(
                node["transformations"]["trans_spec"], ds_info
            )
            hierarchy_reference_datasheet_ids = (
                ds_graph.hierarchy_reference_datasheet_ids(datasheet_id=node_id)
            )
            hierarchy_reference_datasheet_names = [
                ds_info[datasheet_id].name
                for datasheet_id in hierarchy_reference_datasheet_ids
            ]
            databook_id = uuid.UUID(ds_info[node_id].databook_id)
            databook_name = databook_names_dict.get(databook_id)
            if is_sheet_level_graph:
                meta_data = _get_metadata_for_datasheet(
                    client_id, node_id, databook_id, logged_in_user
                )

        if object_type:
            data_origin = source_data_origin.get(object_type, data_origin)

        node_dict = {
            "id": node_id,
            "name": (
                name.replace("_", " ").capitalize()
                if object_type == DatasheetSourceType.REPORT_OBJECT.value
                else name
            ),
            "is_colored": is_colored,
            "is_stale": is_stale,
            "order": node.get("order", 0),
            "data_origin": data_origin,
            "transformation_meta": transformation_meta,
            "hierarchy_meta": hierarchy_reference_datasheet_names,
            "source": source_sheet_name,
            "databook_name": databook_name,
            "meta_data": meta_data,
        }

        nodes.append(node_dict)

    nodes.sort(key=lambda x: (x["order"] is None, x["order"]))

    if is_sheet_level_graph:
        max_height, max_width = ds_graph.calculate_width_and_height_for_graph(
            node_ids=connected_node_ids
        )
    else:
        max_height, max_width = ds_graph.calculate_width_and_height_for_graph(
            node_ids=allowed_nodes
        )

    total_width = node_width * max_width
    total_height = node_height * max_height

    res = {
        "nodes": nodes,
        "edges": edges,
        "totalWidth": total_width,
        "totalHeight": total_height,
    }

    return res


def _update_cloned_datasheet_variables(
    source_vars, cloned_vars, parent_datasheet_id, cloned_datasheet_id
):
    """
    Update source information for cloned datasheet variables.

    Args:
        source_vars: List of variables from the original datasheet
        cloned_vars: List of variables from the cloned datasheet
        parent_datasheet_id: ID of the original datasheet
        cloned_datasheet_id: ID of the cloned datasheet
    """
    system_name_map = {str(var.system_name): var for var in source_vars}
    updated_vars = []

    for cloned_var in cloned_vars:
        source_var = system_name_map.get(str(cloned_var.system_name), {})

        cloned_var.source_type = source_var.source_type
        cloned_var.source_variable_id = source_var.source_variable_id
        cloned_var.source_id = source_var.source_id

        if source_var.source_type == DATASHEET_SOURCE.DATASHEET.value and str(
            source_var.source_id
        ) == str(parent_datasheet_id):
            cloned_var.source_id = cloned_datasheet_id

        updated_vars.append(cloned_var)

    return updated_vars


@transaction.atomic
def clone_datasheet_wrapper(
    request_model: DatasheetCloneRequest, logged_in_user: str, audit: dict
) -> DatasheetCloneResponse:
    """
    Wrapper function to clone a datasheet and update its variables.

    Args:
        request_model (DatasheetCloneRequest): The request model containing cloning details.
        logged_in_user (str): The username of the logged-in user initiating the clone.
        audit (dict): Audit information for tracking the cloning process.

    Returns:
        DatasheetCloneResponse: The result of the cloning process, including the cloned datasheet's ID.

    Raises:
        DatasheetException: If the cloning process fails or user doesn't have permission.
    """
    client_id = request_model.client_id
    datasheet_id = request_model.datasheet_id
    databook_id = request_model.databook_id
    databook_name = request_model.databook_name

    if not does_user_has_permission_to_custom_objects_in_datasheet(
        client_id, databook_id, datasheet_id, logged_in_user
    ):
        raise DatasheetException(
            code="NO_ACCESS_FOR_DATA",
            message="You can't perform this action since you don't have access to the underlying data",
            status=status.HTTP_403_FORBIDDEN,
        )

    try:
        # TODO: Need to re-write the datasheet-clone w.r.t v2 while depricating v1
        result = clone_datasheet(
            client_id,
            databook_id,
            databook_name,
            datasheet_id,
            logged_in_user,
            audit,
            logger,
            to_version="v1",
            order_reverse=True,
        )
        cloned_datasheet_id = result["clone_datasheet_id"]

        datasheet_selector = DatasheetSelector(
            client_id=client_id, datasheet_id=cloned_datasheet_id
        )
        datasheet = datasheet_selector.get_datasheet()

        ## Convert the v2 transformation format to v1 and then feed it to converter as it comming from v1
        ## to populate all the necessary fields in the transformation_spec and variables

        etl_spec_transformer = ETLSpecTransformer(datasheet.transformation_spec)
        etl_spec_transformer.transform()
        datasheet.transformation_spec = etl_spec_transformer.transformations

        DatasheetVersionWriter(
            client_id, cloned_datasheet_id, "v1"
        ).transform_v1_to_v2_and_save(datasheet)
        datasheet.transformation_spec = datasheet.additional_details.pop(
            "transformation_spec_v2", []
        )
        # Clone datasheet views
        ds_view_clone_request_model = DatasheetViewCloneRequest(
            client_id=client_id,
            datasheet_id=datasheet_id,
            clone_datasheet_id=cloned_datasheet_id,  # type: ignore
        )
        clone_datasheet_views(ds_view_clone_request_model)

        datasheet.save()

        return DatasheetCloneResponse(
            status="SUCCESS", clone_datasheet_id=cloned_datasheet_id
        )
    except Exception as error:
        logger.exception(
            "Unknown error occurred while cloning datasheet %s", databook_id
        )
        raise DatasheetException(
            code="UNKNOWN_ERROR",
            message=f"Unknown error occurred while cloning datasheet {databook_id}. Please try again later or contact admin",
        ) from error


def clone_datasheet_variables(
    request_model: DatasheetVariableCloneRequest,
) -> None:
    """
    Creates another copy of all the datasheet variables in {datasheet_id} and assigns it to {clone_datasheet_id}

    Args:
        request_model (DatasheetVariableCloneRequest): The request model containing clone information

    Returns:
        None
    """
    client_id = request_model.client_id
    databook_id = request_model.databook_id
    datasheet_id = request_model.datasheet_id
    clone_databook_id = request_model.clone_databook_id
    clone_datasheet_id = request_model.clone_datasheet_id
    original_to_cloned_datasheet_id_map = (
        request_model.original_to_cloned_datasheet_id_map
    )

    time = timezone.now()
    logger.info("Cloning datasheet with datasheet_id - %s", datasheet_id)
    variables = DatasheetVariableSelector(client_id).get_variables_for_db_ds(
        databook_id, datasheet_id, fetch_only_selected=True
    )

    cloned_variables = []
    for ds_variable in variables:
        cloned_var = ds_variable.copy()

        # Update hierarchy reference if applicable
        if (
            cloned_var["field_order"] > 0
            and is_hierarchy_calculated_field(cloned_var)
            and original_to_cloned_datasheet_id_map is not None
        ):
            _update_hierarchy_reference(
                cloned_var,
                databook_id,
                clone_databook_id,
                original_to_cloned_datasheet_id_map,
            )

        # Update calculated field metadata if applicable
        if cloned_var["field_order"] > 0 and not is_window_function(
            cloned_var["meta_data"]
        ):
            _update_calculated_field_metadata(
                cloned_var, clone_databook_id, clone_datasheet_id
            )

        # Update variable metadata
        cloned_var.update(
            {
                "pk": None,
                "knowledge_begin_date": time,
                "additional_details": None,
                "databook_id": clone_databook_id,
                "datasheet_id": clone_datasheet_id,
                "variable_id": uuid.uuid4(),
                "source_variable_id": None,
                "source_id": None,
                "source_type": None,
            }
        )

        cloned_variables.append(cloned_var)

    DatasheetVariableSelector(client_id).create_ds_variables(cloned_variables)


def _update_hierarchy_reference(
    cloned_var, databook_id, clone_databook_id, original_to_cloned_datasheet_id_map
):
    """
    Updates the hierarchy reference for a cloned variable

    Args:
        cloned_var (dict): The cloned variable to update
        databook_id (str): The original databook ID
        clone_databook_id (str): The cloned databook ID
        original_to_cloned_datasheet_id_map (dict): Mapping of original to cloned datasheet IDs

    This function updates the hierarchy reference sheet ID and reference book ID
    for hierarchy calculated fields. It also updates the source datasheet ID in
    the source calculated field metadata if present.
    """
    hierarchy_ref_sheet_id = get_hierarchy_reference_sheet_id(cloned_var, as_uuid=True)
    reference_book = HierarchyMetaData(
        **cloned_var["meta_data"]["hierarchy"]
    ).reference_book

    if hierarchy_ref_sheet_id != HierarchyUtils.USE_CURRENT_SHEET_DEFAULT_VALUE.value:
        modify_hierarchy_meta_data_for_key(
            cloned_var,
            HierarchyUtils.REFERENCE_SHEET_KEY.value,
            str(
                original_to_cloned_datasheet_id_map.get(
                    hierarchy_ref_sheet_id, hierarchy_ref_sheet_id
                )
            ),
        )

    if str(databook_id) == reference_book:
        modify_hierarchy_meta_data_for_key(
            cloned_var, HierarchyUtils.REFERENCE_BOOK.value, str(clone_databook_id)
        )

    if source_datasheet_id := pydash.get(
        cloned_var, "source_cf_meta_data.datasheet_id"
    ):
        cloned_var["source_cf_meta_data"]["datasheet_id"] = str(
            original_to_cloned_datasheet_id_map.get(
                uuid.UUID(source_datasheet_id), source_datasheet_id
            )
        )


def _update_calculated_field_metadata(
    cloned_var, clone_databook_id, clone_datasheet_id
):
    """
    Updates the calculated field metadata for a cloned variable

    Args:
        cloned_var (dict): The cloned variable to update
        clone_databook_id (str): The cloned databook ID
        clone_datasheet_id (str): The cloned datasheet ID

    This function updates the databook ID and datasheet ID in the infix expression
    of calculated fields if the expression format is "v2".
    """
    infix_expression = cloned_var["meta_data"]["infix"]
    if get_expression_format(infix_expression) == "v2":
        for exp in infix_expression:
            if exp["token_type"] == TokenTypesV2.AST_META.name:
                exp["token"]["databook_id"] = str(clone_databook_id)
                exp["token"]["datasheet_id"] = str(clone_datasheet_id)
        cloned_var["meta_data"]["infix"] = infix_expression


def generate_system_name_variable_id_map(client_id, datasheet_id):
    """
    Generates a mapping of system_name to variable_id for the given datasheet

    Args:
        client_id: The client ID
        datasheet_id: The datasheet ID

    Returns:
        dict: A dictionary mapping system_name to variable_id
    """
    from everstage_ddd.datasheet.selectors.datasheet_variable_selector import (
        DatasheetVariableSelector,
    )

    # Get all variables for the datasheet
    variable_selector = DatasheetVariableSelector(client_id=client_id)
    variables = variable_selector.client_kd_deleted_aware().filter(
        datasheet_id=datasheet_id
    )

    # Create the mapping
    variable_id_map = {var.system_name: str(var.variable_id) for var in variables}
    return variable_id_map


@transaction.atomic
def clone_datasheet_views(request_model: DatasheetViewCloneRequest) -> None:
    """
    Clones all the datasheet views for a datasheet along with filter & pivot details using bulk operations

    Args:
        request_model (DatasheetViewCloneRequest): The request model containing clone information
    """
    client_id = request_model.client_id
    datasheet_id = request_model.datasheet_id
    clone_datasheet_id = request_model.clone_datasheet_id
    system_name_variable_id_map = generate_system_name_variable_id_map(
        client_id, clone_datasheet_id
    )

    # Get all existing views
    views = ds_selectors.DatasheetViewSelector(
        client_id=client_id, datasheet_id=datasheet_id
    ).get_datasheet_views()

    # Prepare bulk data
    pivot_bulk_data = []
    filter_bulk_data = []
    view_bulk_data = []

    # Create mappings for new IDs
    pivot_mapping = {}  # old_pivot_id -> new_pivot_id
    filter_mapping = {}  # old_filter_id -> new_filter_id

    # First, prepare pivot and filter data
    for view in views:
        if view.pivot_id:
            view_pivot_record = ds_selectors.DatasheetViewPivotSelector(
                client_id=client_id, pivot_id=view.pivot_id
            ).get_pivot_record()
            new_pivot_id = uuid.uuid4()
            pivot_mapping[view.pivot_id] = new_pivot_id

            # Deep copy the pivot data to avoid modifying the original
            pivot_data_copy = copy.deepcopy(view_pivot_record.pivot_data)

            # Update variable IDs in pivot data if system_name_variable_id_map is provided
            if system_name_variable_id_map:
                pivot_data_copy = _update_variable_ids_in_pivot_data(
                    pivot_data_copy, system_name_variable_id_map
                )

            pivot_data = {
                "pivot_id": new_pivot_id,
                "client_id": client_id,
                "pivot_data": pivot_data_copy,
            }
            pivot_bulk_data.append(pivot_data)

        if view.filter_id:
            view_filter_record = ds_selectors.DatasheetViewFilterSelector(
                client_id=client_id, filter_id=view.filter_id
            ).get_filter_record()
            new_filter_id = uuid.uuid4()
            filter_mapping[view.filter_id] = new_filter_id

            # Deep copy the filter data to avoid modifying the original
            filter_data_copy = copy.deepcopy(view_filter_record.filter_data)

            # Update variable IDs in filter data if system_name_variable_id_map is provided
            if system_name_variable_id_map:
                filter_data_copy = _update_variable_ids_in_filter_data(
                    filter_data_copy, system_name_variable_id_map
                )

            filter_data = {
                "filter_id": new_filter_id,
                "client_id": client_id,
                "filter_data": filter_data_copy,
            }
            filter_bulk_data.append(filter_data)

    # Prepare view data with new pivot and filter references
    for view in views:
        view_data = model_to_dict(view)
        view_data["pk"] = None
        view_data["temporal_id"] = None
        view_data["view_id"] = uuid.uuid4()
        view_data["datasheet_id"] = clone_datasheet_id
        view_data["pivot_id"] = pivot_mapping.get(
            view.pivot_id
        )  # Map to new pivot_id if exists
        view_data["filter_id"] = filter_mapping.get(
            view.filter_id
        )  # Map to new filter_id if exists
        view_bulk_data.append(view_data)

    # Bulk create pivot, filter and view data
    if pivot_bulk_data:
        pivot_selector = ds_selectors.DatasheetViewPivotSelector(client_id=client_id)
        pivot_selector.bulk_create_view_pivots(pivot_bulk_data)

    if filter_bulk_data:
        filter_selector = ds_selectors.DatasheetViewFilterSelector(client_id=client_id)
        filter_selector.bitemporal_bulk_create(filter_bulk_data)

    if view_bulk_data:
        ds_selectors.DatasheetViewSelector(
            client_id=client_id, datasheet_id=clone_datasheet_id
        ).bitemporal_bulk_create(data=view_bulk_data)


def _update_variable_ids_in_filter_data(filter_data, system_name_variable_id_map):
    """
    Update variable IDs in filter data based on system names

    Args:
        filter_data: The filter data to update
        system_name_variable_id_map: Mapping of system_name to variable_id
        datasheet_id: The datasheet ID

    Returns:
        Updated filter data
    """

    if not filter_data:
        return filter_data

    updated_filter_data = copy.deepcopy(filter_data)

    def update_datasheet_variable_token(token_obj):
        """Update a DATASHEET_VARIABLES token with variable_id"""
        if isinstance(token_obj, dict) and "system_name" in token_obj:
            system_name = token_obj.get("system_name")
            if system_name and system_name in system_name_variable_id_map:
                token_obj["variable_id"] = system_name_variable_id_map[system_name]

    def recursive_update(item):
        """Recursively update tokens in the filter data structure"""
        if not isinstance(item, dict):
            return

        # Handle token_type DATASHEET_VARIABLES
        if item.get("token_type") == "DATASHEET_VARIABLES" and "token" in item:
            update_datasheet_variable_token(item["token"])

        # Handle token_type FUNCTIONS with args that might contain DATASHEET_VARIABLES
        elif item.get("token_type") == "FUNCTIONS" and "token" in item:
            token = item["token"]
            if "args" in token and isinstance(token["args"], list):
                for arg in token["args"]:
                    recursive_update(arg)

        # Process token if it exists
        if "token" in item and isinstance(item["token"], dict):
            # Check if token has args that need processing
            token = item["token"]
            if "args" in token and isinstance(token["args"], list):
                for arg in token["args"]:
                    recursive_update(arg)

    # Handle different filter data formats
    if isinstance(updated_filter_data, list):
        # Process each item in the list
        for item in updated_filter_data:
            recursive_update(item)
    elif isinstance(updated_filter_data, dict):
        # Check if it's a token-based structure with 'filter_data' key
        if "filter_data" in updated_filter_data and isinstance(
            updated_filter_data["filter_data"], list
        ):
            for item in updated_filter_data["filter_data"]:
                recursive_update(item)
        # Otherwise, process the dictionary directly
        else:
            recursive_update(updated_filter_data)

    return updated_filter_data


def _update_variable_ids_in_pivot_data(pivot_data, system_name_variable_id_map):
    """
    Update variable IDs in pivot data based on system names

    Args:
        pivot_data: The pivot data to update
        system_name_variable_id_map: Mapping of system_name to variable_id
        datasheet_id: The datasheet ID

    Returns:
        Updated pivot data
    """

    if not pivot_data:
        return pivot_data

    updated_pivot_data = copy.deepcopy(pivot_data)

    # Collect all system_names used in the pivot data
    used_columns = set()

    # Add system_names from index
    if "index" in updated_pivot_data and isinstance(updated_pivot_data["index"], list):
        for col in updated_pivot_data["index"]:
            if isinstance(col, str):
                used_columns.add(col)

    # Add system_names from values
    if "values" in updated_pivot_data and isinstance(
        updated_pivot_data["values"], list
    ):
        for col in updated_pivot_data["values"]:
            if isinstance(col, str):
                used_columns.add(col)

    # Add system_names from aggfunc
    if "aggfunc" in updated_pivot_data and isinstance(
        updated_pivot_data["aggfunc"], dict
    ):
        for col in updated_pivot_data["aggfunc"]:
            if isinstance(col, str):
                used_columns.add(col)

    # Add system_names from columns
    if "columns" in updated_pivot_data and isinstance(
        updated_pivot_data["columns"], list
    ):
        for col in updated_pivot_data["columns"]:
            if isinstance(col, str):
                used_columns.add(col)

    # Create or update variable_id_map
    variable_id_map = updated_pivot_data.get("variable_id_map", {})
    if variable_id_map is None:
        variable_id_map = {}

    # Add missing variable_ids to the map
    for system_name in used_columns:
        if system_name in system_name_variable_id_map:
            variable_id_map[system_name] = system_name_variable_id_map[system_name]

    # Update the variable_id_map in the pivot data
    updated_pivot_data["variable_id_map"] = variable_id_map

    return updated_pivot_data


def get_output_columns_for_transformation(client_id, datasheet_id, data):
    transformation = data["transformation_spec"]
    prev_output_columns = data["output_columns"]

    transformation["is_valid"] = False
    transformation["output_columns"] = []
    source_name = fetch_source_name_of_variable(
        client_id=client_id,
        source_id=datasheet_id,
        source_type=DatasheetSourceType.DATASHEET.value,
    )

    if transformation["type"] == TransformationType.JOIN.value:
        if len(transformation["on"]["lhs"]) != len(transformation["on"]["rhs"]):
            raise DatasheetException(
                code="COLUMN_MISMATCH",
                message="Number of join keys and their data types should match in both datasheets",
            )
        for index, lhs_val in enumerate(transformation["on"]["lhs"]):
            if (
                lhs_val["data_type_id"]
                != transformation["on"]["rhs"][index]["data_type_id"]
            ):
                raise DatasheetException(
                    code="DATA_TYPE_MISMATCH",
                    message="Columns used to join sheets must have the same datatypes",
                )

        if transformation["with"] is None:
            raise DatasheetException(
                code="INVALID_PARAMETER",
                message="Cannot join with empty datasheet",
            )

        for lhs_column in transformation["columns"]["lhs"]:
            lhs_column["system_name"] = f"lhs_{lhs_column['system_name']}"

        for rhs_column in transformation["columns"]["rhs"]:
            rhs_column["system_name"] = f"rhs_{rhs_column['system_name']}"
            rhs_column["source_id"] = transformation["with"]
            rhs_column["source_type"] = DatasheetSourceType.DATASHEET.value

        transformation["output_columns"] = (
            transformation["columns"]["lhs"] + transformation["columns"]["rhs"]
        )
    elif transformation["type"] == TransformationType.UNION.value:
        if any(
            [
                len(column["lhs"]) == 0 or len(column["rhs"]) == 0
                for column in transformation["on"]
            ]
        ):
            raise DatasheetException(
                code="COLUMN_MISMATCH",
                message="Number of look up columns on lhs and rhs should be same in union transformation",
            )

        for column in transformation["on"]:
            lhs_column = column["lhs"]
            rhs_column = column["rhs"]
            if lhs_column["data_type_id"] != rhs_column["data_type_id"]:
                raise DatasheetException(
                    code="DATA_TYPE_MISMATCH",
                    message=f"Data types of {lhs_column['display_name']} and {rhs_column['display_name']} should be same in union",
                )

        transformation["output_columns"] = [
            column["lhs"] | {"is_primary": True} for column in transformation["on"]
        ]

    elif transformation["type"] == TransformationType.GET_USER_PROPERTIES.value:
        transformation["output_columns"] = prev_output_columns
        for user_property in transformation["user_properties"]:
            user_property["output_variable_system_name"] = "_".join(
                [
                    "up",
                    transformation["email_column"]["value"],
                    transformation["as_of_date_column"]["value"],
                    user_property["user_property_system_name"],
                    transformation["key"][-4:],
                ]
            ).lower()
            transformation["output_columns"].append(
                {
                    "system_name": user_property["output_variable_system_name"],
                    "display_name": user_property["display_name"],
                    "data_type_id": user_property["data_type_id"],
                    "variable_id": (
                        user_property["user_property_system_name"] + "_user"
                    ),
                    "is_selected": True,
                    "is_primary": False,
                    "source_id": "user",
                    "source_type": DatasheetSourceType.REPORT_OBJECT.value,
                    "source_name_history": user_property["display_name"]
                    + "<<"
                    + "user",
                    "source_variable_id": None,
                    "source_cf_meta_data": None,
                    "meta_data": None,
                }
            )
    elif transformation["type"] == TransformationType.GROUP_BY.value:
        prev_output_column_map = {
            column["system_name"]: column for column in prev_output_columns
        }
        output_columns = [
            column | {"is_primary": True} for column in transformation["by"]
        ]
        for aggregation in transformation["aggregations"]:
            column_name = aggregation["of"]
            aggregation["col_name"] = f"{aggregation['function'].lower()}_{column_name}"
            data_type_id = (
                get_data_types()[
                    GroupByAggDatatypesMap[aggregation["function"]].upper()
                ]
                if GroupByAggDatatypesMap[aggregation["function"]] is not None
                else prev_output_column_map[column_name]["data_type_id"]
            )
            aggregation["data_type_id"] = data_type_id
            display_name = f"{aggregation['function']}::{column_name.upper()}"
            output_columns.append(
                {
                    "system_name": aggregation["col_name"],
                    "column_name": aggregation["col_name"],
                    "display_name": display_name,
                    "data_type_id": data_type_id,
                    "variable_id": uuid4(),
                    "source_id": str(datasheet_id),
                    "source_type": DatasheetSourceType.DATASHEET.value,
                    "source_variable_id": None,
                    "is_selected": True,
                    "is_primary": False,
                    "source_cf_meta_data": None,
                    "meta_data": None,
                    "system_generated": True,
                    "source_name_history": display_name + " << " + source_name,
                }
            )
        transformation["output_columns"] = output_columns

    elif transformation["type"] in [
        TransformationType.ADVANCED_FILTER_V2.value,
        TransformationType.ADVANCED_FILTER.value,
        TransformationType.FILTER.value,
    ]:
        transformation["output_columns"] = prev_output_columns

    elif transformation["type"] == TransformationType.FLATTEN.value:
        hierarchy_col = list(
            filter(
                lambda variable: variable["system_name"] == transformation["col_name"],
                prev_output_columns,
            )
        )[0]
        hierarchy_display_name = hierarchy_col["display_name"]
        source_id = hierarchy_col["source_id"][-4:]
        transformation["flattened_col_name"] = (
            f"{transformation['col_name']}_{transformation['output_data_type'].lower()}_{source_id}"
        )
        display_name = (
            hierarchy_display_name
            + " as "
            + transformation["output_data_type"]
            + "-flattened"
        )
        transformation["output_columns"].append(
            {
                "system_name": transformation["flattened_col_name"],
                "display_name": display_name,
                "data_type_id": get_data_types()[
                    transformation["output_data_type"].upper()
                ],
                "variable_id": uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
            }
        )

    elif transformation["type"] == TransformationType.TEMPORAL_SPLICE.value:
        transformation["output_columns"] = [
            {
                "system_name": "ts_employee_email_id",
                "display_name": "TS::Employee Email Id",
                "data_type_id": 12,
                "variable_id": uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
                "source_name_history": "TS::Employee Email Id << " + source_name,
            },
            {
                "system_name": "ts_effective_start_date",
                "display_name": "TS::Effective Start Date",
                "data_type_id": 2,
                "variable_id": uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
                "source_name_history": "TS::Effective Start Date << " + source_name,
            },
            {
                "system_name": "ts_effective_end_date",
                "display_name": "TS::Effective End Date",
                "data_type_id": 2,
                "variable_id": uuid4(),
                "is_selected": True,
                "is_primary": True,
                "source_id": str(datasheet_id),
                "source_type": DatasheetSourceType.DATASHEET.value,
                "source_variable_id": None,
                "source_cf_meta_data": None,
                "meta_data": None,
                "system_generated": True,
                "source_name_history": "TS::Effective End Date << " + source_name,
            },
        ]

        for index, data_source in enumerate(transformation["meta"]):
            if index == 0:
                data_source["input_columns"] = prev_output_columns
            else:
                data_source["input_columns"] = datasheet_fetch_source_variables(
                    client_id=client_id,
                    source_id=data_source["source_id"],
                    source_type=data_source["source_type"],
                )

            variable_ids_to_be_removed = set(
                [data_source["email_id_column"]["variable_id"]]
            )

            if data_source["has_effective_dates"]:
                if data_source["start_date_column"]["value"]:
                    variable_ids_to_be_removed.add(
                        data_source["start_date_column"]["variable_id"]
                    )

                if data_source["end_date_column"]["value"]:
                    variable_ids_to_be_removed.add(
                        data_source["end_date_column"]["variable_id"]
                    )

            data_source["output_columns"] = []
            for variable in data_source["input_columns"]:
                variable["system_name"] = f"ts_{index + 1}_{variable['system_name']}"
                variable["is_primary"] = False
                # For the first data source, source_id and source_type will be
                # computed from previous transformation variables
                # For the subsequent data sources, source_id and source_type will be computed
                if index != 0:
                    variable["source_id"] = data_source["source_id"]
                    variable["source_type"] = data_source["source_type"]

                if str(variable["variable_id"]) not in variable_ids_to_be_removed:
                    data_source["output_columns"].append(variable)

            transformation["output_columns"].extend(data_source["output_columns"])

    else:
        raise DatasheetException(
            code="INVALID_TRANSFORMATION_TYPE",
            message="Invalid transformation type",
        )

    return {"transformation_spec": transformation}


def get_force_skipped_datasheet_ids(client_id: int) -> list[str]:
    """
    Return a list of datasheet IDs that are force skipped (archived) for the client.
    """
    return DatasheetSelector(client_id=client_id).get_force_skipped_datasheet_ids()


def get_sheet_timeline(sheet_id: str, databook_id: str, client_id: int) -> list[dict]:
    """
    Returns a chronological timeline of all tracked actions for a given sheet.
    Each event contains: timestamp, action_type, performed_by, details.
    """
    from everstage_ddd.datasheet.models import (
        Datasheet,
        DatasheetAdjustments,
        DatasheetPermissions,
        DatasheetTransformation,
        DatasheetVariable,
    )

    timeline = []

    def safe_get_attr(obj, attr_name, default=None):
        """Safely get attribute from object, return default if not found"""
        value = getattr(obj, attr_name, default)
        return value if value is not None else "-"

    def get_performed_by(obj):
        """Get performed_by from object, checking additional_details if not found in direct fields"""
        # First try direct fields
        performed_by = safe_get_attr(obj, "created_by")
        if performed_by == "-":
            performed_by = safe_get_attr(obj, "updated_by")

        # If still not found, check additional_details
        if performed_by == "-":
            additional_details = safe_get_attr(obj, "additional_details", {})
            if isinstance(additional_details, dict):
                performed_by = additional_details.get("updated_by", "-")

        return performed_by

    def is_calculated_field(variable):
        """Check if a variable is a calculated field by examining meta_data"""
        meta_data = safe_get_attr(variable, "meta_data")
        if meta_data and isinstance(meta_data, dict):
            # Check for calculated field indicators
            return bool(
                meta_data.get("ast")
                or meta_data.get("infix")
                or meta_data.get("criteria_type")
            )
        return False

    # 1. Sheet Created, Renamed, Deleted/Archived, Cloned, Refreshed/Generated
    sheet = Datasheet.objects.filter(
        datasheet_id=sheet_id, databook_id=databook_id, client_id=client_id
    ).first()
    if sheet:
        # Use knowledge_begin_date as creation timestamp (MultiTenantTemporal doesn't have created_at)
        timeline.append(
            {
                "timestamp": safe_get_attr(sheet, "knowledge_begin_date"),
                "created_at": safe_get_attr(sheet, "knowledge_begin_date"),
                "updated_at": (
                    safe_get_attr(sheet, "knowledge_end_date")
                    if safe_get_attr(sheet, "knowledge_end_date") != "-"
                    else safe_get_attr(sheet, "knowledge_begin_date")
                ),
                "action_type": "sheet_created",
                "performed_by": get_performed_by(sheet),
                "details": {"name": safe_get_attr(sheet, "name")},
            }
        )

        # Check if sheet is archived or deleted
        if safe_get_attr(sheet, "is_deleted", False):
            timeline.append(
                {
                    "timestamp": safe_get_attr(sheet, "knowledge_end_date"),
                    "created_at": safe_get_attr(sheet, "knowledge_begin_date"),
                    "updated_at": safe_get_attr(sheet, "knowledge_end_date"),
                    "action_type": "sheet_deleted",
                    "performed_by": get_performed_by(sheet),
                    "details": {"name": safe_get_attr(sheet, "name")},
                }
            )

        # Check if sheet was generated
        if safe_get_attr(sheet, "is_datasheet_generated", False):
            timeline.append(
                {
                    "timestamp": safe_get_attr(sheet, "data_last_updated_at"),
                    "created_at": safe_get_attr(sheet, "knowledge_begin_date"),
                    "updated_at": safe_get_attr(sheet, "data_last_updated_at"),
                    "action_type": "sheet_generated",
                    "performed_by": get_performed_by(sheet),
                    "details": {"name": safe_get_attr(sheet, "name")},
                }
            )

    # 2. Transformations
    for t in DatasheetTransformation.objects.filter(
        datasheet_id=sheet_id, client_id=client_id
    ):
        created_at = safe_get_attr(t, "created_at")
        updated_at = safe_get_attr(t, "updated_at")

        timeline.append(
            {
                "timestamp": created_at,
                "created_at": created_at,
                "updated_at": updated_at if updated_at != "-" else created_at,
                "action_type": "transformation_added",
                "performed_by": get_performed_by(t),
                "details": {
                    "type": safe_get_attr(t, "type"),
                    "spec": safe_get_attr(t, "spec"),
                },
            }
        )

        # Check if transformation was updated
        if updated_at != "-" and created_at != "-" and updated_at != created_at:
            timeline.append(
                {
                    "timestamp": updated_at,
                    "created_at": created_at,
                    "updated_at": updated_at,
                    "action_type": "transformation_edited",
                    "performed_by": get_performed_by(t),
                    "details": {
                        "type": safe_get_attr(t, "type"),
                        "spec": safe_get_attr(t, "spec"),
                    },
                }
            )

    # 3. Variables (add/edit/delete/select/deselect) - Only show calculated fields
    for v in DatasheetVariable.objects.filter(
        datasheet_id=sheet_id, client_id=client_id
    ):
        # Only process calculated fields
        if not is_calculated_field(v):
            continue

        # Only show calculated fields that were added in the source sheet
        # (source_variable_id is None for source calculated fields)
        # Derived sheets will have source_variable_id pointing to the source sheet
        source_var_id = safe_get_attr(v, "source_variable_id")
        if source_var_id is not None and source_var_id != "-":
            continue

        knowledge_begin = safe_get_attr(v, "knowledge_begin_date")
        knowledge_end = safe_get_attr(v, "knowledge_end_date")

        timeline.append(
            {
                "timestamp": knowledge_begin,
                "created_at": knowledge_begin,
                "updated_at": (
                    knowledge_end if knowledge_end != "-" else knowledge_begin
                ),
                "action_type": "calculated_field_added",
                "performed_by": get_performed_by(v),
                "details": {
                    "name": safe_get_attr(v, "display_name"),
                },
            }
        )

        # Check if variable was edited (knowledge_end_date indicates changes)
        if (
            knowledge_end != "-"
            and knowledge_begin != "-"
            and knowledge_end != knowledge_begin
        ):
            timeline.append(
                {
                    "timestamp": knowledge_end,
                    "created_at": knowledge_begin,
                    "updated_at": knowledge_end,
                    "action_type": "calculated_field_edited",
                    "performed_by": get_performed_by(v),
                    "details": {
                        "name": safe_get_attr(v, "display_name"),
                    },
                }
            )

        # Check if variable selection status is available
        is_selected = safe_get_attr(v, "is_selected")
        if is_selected != "-":
            timeline.append(
                {
                    "timestamp": knowledge_begin,
                    "created_at": knowledge_begin,
                    "updated_at": (
                        knowledge_end if knowledge_end != "-" else knowledge_begin
                    ),
                    "action_type": (
                        "calculated_field_selected"
                        if is_selected
                        else "calculated_field_deselected"
                    ),
                    "performed_by": get_performed_by(v),
                    "details": {"name": safe_get_attr(v, "display_name")},
                }
            )

    # 4. Adjustments
    for a in DatasheetAdjustments.objects.filter(
        datasheet_id=sheet_id, client_id=client_id
    ):
        knowledge_begin = safe_get_attr(a, "knowledge_begin_date")
        knowledge_end = safe_get_attr(a, "knowledge_end_date")

        timeline.append(
            {
                "timestamp": knowledge_begin,
                "created_at": knowledge_begin,
                "updated_at": (
                    knowledge_end if knowledge_end != "-" else knowledge_begin
                ),
                "action_type": "adjustment_added",
                "performed_by": get_performed_by(a),
                "details": {
                    "operation": safe_get_attr(a, "operation"),
                    "comments": safe_get_attr(a, "comments"),
                },
            }
        )

    # 5. Permissions
    for p in DatasheetPermissions.objects.filter(
        datasheet_id=sheet_id, client_id=client_id
    ):
        knowledge_begin = safe_get_attr(p, "knowledge_begin_date")
        knowledge_end = safe_get_attr(p, "knowledge_end_date")

        timeline.append(
            {
                "timestamp": knowledge_begin,
                "created_at": knowledge_begin,
                "updated_at": (
                    knowledge_end if knowledge_end != "-" else knowledge_begin
                ),
                "action_type": "permission_changed",
                "performed_by": get_performed_by(p),
                "details": {
                    "permission_set_name": safe_get_attr(p, "permission_set_name"),
                },
            }
        )

    # Filter out entries with invalid timestamps and sort chronologically
    timeline = [e for e in timeline if e["timestamp"] and e["timestamp"] != "-"]
    timeline.sort(key=lambda x: x["timestamp"])
    return timeline
