import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useEffect, useState } from "react";
// Simple icon components as fallbacks
const ChevronDownIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 9l-7 7-7-7"
    />
  </svg>
);

const ChevronUpIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M5 15l7-7 7 7"
    />
  </svg>
);

const MagnifyingGlassIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
);

const ArrowPathIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
    />
  </svg>
);

const EyeSlashIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
    />
  </svg>
);

const AdjustmentsHorizontalIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
    />
  </svg>
);

const PlusIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 4v16m8-8H4"
    />
  </svg>
);

const PencilIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
    />
  </svg>
);

const CalculatorIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
    />
  </svg>
);

const CogIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
  </svg>
);

const ShieldCheckIcon = ({ className }) => (
  <svg
    className={className}
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
    />
  </svg>
);

import { useAuthStore } from "~/GlobalStores/AuthStore";

import { EverButton } from "./ever-button/EverButton";
import { EverCheckbox } from "./ever-checkbox/EverCheckbox";
import { EverGroupAvatar } from "./EverGroupAvatar";
import { EverInput } from "./EverInput";
import { EverTg } from "./EverTypography";
import { ReadOnlyExpression } from "./expression-designer/ReadOnlyExpression";

dayjs.extend(relativeTime);

// Action type configurations with icons and labels
const actionTypeConfig = {
  sheet_created: {
    label: "Created Sheet",
    icon: PlusIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    tag: "add",
  },
  sheet_renamed: {
    label: "Renamed Sheet",
    icon: PencilIcon,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    tag: "edit",
  },
  sheet_deleted: {
    label: "Deleted Sheet",
    icon: EyeSlashIcon,
    color: "text-red-600",
    bgColor: "bg-red-50",
    tag: "delete",
  },
  sheet_generated: {
    label: "Generated Sheet",
    icon: CogIcon,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    tag: "generate",
  },
  transformation_added: {
    label: "Added Data Transformation",
    icon: AdjustmentsHorizontalIcon,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    tag: "add",
  },
  transformation_edited: {
    label: "Updated Data Transformation",
    icon: PencilIcon,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    tag: "edit",
  },
  calculated_field_added: {
    label: "Added Calculated Field",
    icon: CalculatorIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    tag: "add",
  },
  calculated_field_edited: {
    label: "Updated Calculated Field",
    icon: CalculatorIcon,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    tag: "edit",
  },
  calculated_field_selected: {
    label: "Selected Calculated Field",
    icon: ShieldCheckIcon,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
    tag: "select",
  },
  calculated_field_deselected: {
    label: "Deselected Calculated Field",
    icon: EyeSlashIcon,
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    tag: "deselect",
  },
  adjustment_added: {
    label: "Added Adjustment",
    icon: CalculatorIcon,
    color: "text-indigo-600",
    bgColor: "bg-indigo-50",
    tag: "add",
  },
  permission_created: {
    label: "Created Permission",
    icon: ShieldCheckIcon,
    color: "text-green-600",
    bgColor: "bg-green-50",
    tag: "add",
  },
  permission_updated: {
    label: "Updated Permission",
    icon: ShieldCheckIcon,
    color: "text-orange-600",
    bgColor: "bg-orange-50",
    tag: "edit",
  },
};

const SheetTimeline = ({ datasheetId, databookId, isVisible = true }) => {
  const [timeline, setTimeline] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedActionTypes, setSelectedActionTypes] = useState(
    new Set(Object.keys(actionTypeConfig))
  );
  const [selectedUsers, setSelectedUsers] = useState(new Set());
  const [showFilters, setShowFilters] = useState(true);
  const [expandedItems, setExpandedItems] = useState(new Set());
  const { accessToken } = useAuthStore();

  useEffect(() => {
    // Only fetch data when the component is visible
    if (!isVisible || !datasheetId || !databookId || !accessToken) {
      if (!isVisible) {
        setLoading(false);
      }
      return;
    }

    setLoading(true);
    fetch(`/datasheets/${datasheetId}/timeline/${databookId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        const timelineData = data.timeline || [];
        setTimeline(timelineData);

        // Extract unique users for filter
        const users = new Set(
          timelineData.map((item) => item.performed_by).filter(Boolean)
        );
        setSelectedUsers(new Set(users));
      })
      .catch((error) => {
        console.error("Error fetching timeline:", error);
        setTimeline([]);
      })
      .finally(() => setLoading(false));
  }, [isVisible, datasheetId, databookId, accessToken]);

  // Filter timeline based on search and selections
  const filteredTimeline = timeline.filter((item) => {
    const matchesSearch =
      !searchTerm ||
      item.action_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.performed_by &&
        item.performed_by.toLowerCase().includes(searchTerm.toLowerCase())) ||
      Object.values(item.details || {}).some((detail) =>
        String(detail).toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesActionType = selectedActionTypes.has(item.action_type);
    const matchesUser =
      !item.performed_by || selectedUsers.has(item.performed_by);

    return matchesSearch && matchesActionType && matchesUser;
  });

  const uniqueUsers = [
    ...new Set(timeline.map((item) => item.performed_by).filter(Boolean)),
  ];

  const toggleActionType = (actionType) => {
    const newSet = new Set(selectedActionTypes);
    if (newSet.has(actionType)) {
      newSet.delete(actionType);
    } else {
      newSet.add(actionType);
    }
    setSelectedActionTypes(newSet);
  };

  const toggleAllActionTypes = () => {
    const allActionTypes = Object.keys(actionTypeConfig);
    if (selectedActionTypes.size === allActionTypes.length) {
      // If all are selected, deselect all
      setSelectedActionTypes(new Set());
    } else {
      // If not all are selected, select all
      setSelectedActionTypes(new Set(allActionTypes));
    }
  };

  const toggleUser = (user) => {
    const newSet = new Set(selectedUsers);
    if (newSet.has(user)) {
      newSet.delete(user);
    } else {
      newSet.add(user);
    }
    setSelectedUsers(newSet);
  };

  const toggleAllUsers = () => {
    if (selectedUsers.size === uniqueUsers.length) {
      // If all are selected, deselect all
      setSelectedUsers(new Set());
    } else {
      // If not all are selected, select all
      setSelectedUsers(new Set(uniqueUsers));
    }
  };

  const toggleExpanded = (timestamp) => {
    const newSet = new Set(expandedItems);
    if (newSet.has(timestamp)) {
      newSet.delete(timestamp);
    } else {
      newSet.add(timestamp);
    }
    setExpandedItems(newSet);
  };

  const refreshTimeline = () => {
    // Only refresh if the component is visible
    if (!isVisible || !datasheetId || !databookId || !accessToken) {
      return;
    }

    setLoading(true);
    fetch(`/datasheets/${datasheetId}/timeline/${databookId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        const timelineData = data.timeline || [];
        setTimeline(timelineData);

        // Extract unique users for filter
        const users = new Set(
          timelineData.map((item) => item.performed_by).filter(Boolean)
        );
        setSelectedUsers(new Set(users));
      })
      .catch((error) => {
        console.error("Error fetching timeline:", error);
        setTimeline([]);
      })
      .finally(() => setLoading(false));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading timeline...</div>
      </div>
    );
  }

  return (
    <div className="flex h-full bg-gray-50">
      {/* Left Sidebar - Filters */}
      {showFilters && (
        <div className="w-72 bg-white border-r border-gray-200 p-6 overflow-y-auto">
          {/* Filters Header */}
          <div className="flex items-center gap-3 mb-6">
            <AdjustmentsHorizontalIcon className="w-5 h-5 text-gray-600" />
            <EverTg.Heading4 className="text-gray-900">Filters</EverTg.Heading4>
          </div>

          {/* Search */}
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-3">
              <MagnifyingGlassIcon className="w-4 h-4 text-gray-600" />
              <EverTg.Text className="text-gray-700 font-medium">
                Search
              </EverTg.Text>
            </div>
            <EverInput
              placeholder="Search changes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Action Types */}
          <div className="mb-8">
            <EverTg.Text className="text-gray-700 font-medium mb-4">
              Action Types
            </EverTg.Text>
            <div className="space-y-3">
              {/* Select All Action Types */}
              <div className="flex items-center gap-3 pb-2 border-b border-gray-100">
                <EverCheckbox
                  checked={
                    selectedActionTypes.size ===
                    Object.keys(actionTypeConfig).length
                  }
                  indeterminate={
                    selectedActionTypes.size > 0 &&
                    selectedActionTypes.size <
                      Object.keys(actionTypeConfig).length
                  }
                  onChange={toggleAllActionTypes}
                />
                <EverTg.Caption className="text-gray-700 text-sm font-medium">
                  Select All
                </EverTg.Caption>
              </div>
              {Object.entries(actionTypeConfig).map(([actionType, config]) => {
                const IconComponent = config.icon;
                return (
                  <div key={actionType} className="flex items-center gap-3">
                    <EverCheckbox
                      checked={selectedActionTypes.has(actionType)}
                      onChange={() => toggleActionType(actionType)}
                    />
                    <div className={`p-1.5 rounded-md ${config.bgColor}`}>
                      <IconComponent
                        className={`w-3.5 h-3.5 ${config.color}`}
                      />
                    </div>
                    <EverTg.Caption className="text-gray-700 text-sm">
                      {config.label}
                    </EverTg.Caption>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Users */}
          {uniqueUsers.length > 0 && (
            <div>
              <EverTg.Text className="text-gray-700 font-medium mb-4">
                Users
              </EverTg.Text>
              <div className="space-y-3">
                {/* Select All Users */}
                <div className="flex items-center gap-3 pb-2 border-b border-gray-100">
                  <EverCheckbox
                    checked={selectedUsers.size === uniqueUsers.length}
                    indeterminate={
                      selectedUsers.size > 0 &&
                      selectedUsers.size < uniqueUsers.length
                    }
                    onChange={toggleAllUsers}
                  />
                  <EverTg.Caption className="text-gray-700 text-sm font-medium">
                    Select All
                  </EverTg.Caption>
                </div>
                {uniqueUsers.map((user) => (
                  <div key={user} className="flex items-center gap-3">
                    <EverCheckbox
                      checked={selectedUsers.has(user)}
                      onChange={() => toggleUser(user)}
                    />
                    <EverTg.Caption className="text-gray-700 text-sm">
                      {user}
                    </EverTg.Caption>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Action Bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <EverTg.Heading4 className="text-gray-900 mb-1">
                Recent Changes
              </EverTg.Heading4>
              <EverTg.Caption className="text-gray-500">
                {filteredTimeline.length} entries
              </EverTg.Caption>
            </div>

            <div className="flex items-center gap-3">
              <EverButton
                type="ghost"
                size="small"
                onClick={refreshTimeline}
                className="flex items-center gap-2"
              >
                <ArrowPathIcon className="w-4 h-4" />
                Refresh
              </EverButton>

              <EverButton
                type="ghost"
                size="small"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <EyeSlashIcon className="w-4 h-4" />
                {showFilters ? "Hide" : "Show"} Filters
              </EverButton>
            </div>
          </div>
        </div>

        {/* Timeline Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {filteredTimeline.length === 0 ? (
            <div className="text-center py-16">
              <EverTg.Text className="text-gray-500">
                {timeline.length === 0
                  ? "No timeline data available."
                  : "No changes match your filters."}
              </EverTg.Text>
            </div>
          ) : (
            <div className="relative">
              {/* Continuous timeline line */}
              <div className="absolute left-6 top-6 bottom-6 w-0.5 bg-gray-200"></div>

              {filteredTimeline.map((item, index) => {
                const config = actionTypeConfig[item.action_type];
                const IconComponent = config?.icon || CogIcon;
                const itemId = `${item.timestamp}-${item.action_type}-${index}`;
                const isExpanded = expandedItems.has(itemId);

                return (
                  <div
                    key={itemId}
                    className="relative flex items-start gap-4 pb-8 last:pb-0"
                  >
                    {/* Timeline dot with icon */}
                    <div className="relative z-10 flex-shrink-0">
                      <div
                        className={`w-12 h-12 rounded-full border-4 border-white shadow-sm flex items-center justify-center ${
                          config?.bgColor || "bg-gray-50"
                        }`}
                      >
                        <IconComponent
                          className={`w-5 h-5 ${
                            config?.color || "text-gray-600"
                          }`}
                        />
                      </div>
                    </div>

                    {/* Timeline content card */}
                    <div className="flex-1 bg-white rounded-xl border border-gray-200 p-6 shadow-sm hover:shadow-md transition-shadow ml-2">
                      {/* Content */}
                      <div className="w-full">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <EverTg.Text className="font-semibold text-gray-900 text-base">
                              {config?.label || item.action_type}
                            </EverTg.Text>
                            {config?.tag && (
                              <span
                                className={`px-3 py-1 text-xs rounded-full font-medium ${
                                  config.tag === "add"
                                    ? "bg-green-100 text-green-700"
                                    : config.tag === "edit"
                                    ? "bg-orange-100 text-orange-700"
                                    : config.tag === "delete"
                                    ? "bg-red-100 text-red-700"
                                    : "bg-gray-100 text-gray-700"
                                }`}
                              >
                                {config.tag}
                              </span>
                            )}
                          </div>
                          <EverTg.Caption className="text-gray-500 text-sm">
                            {dayjs(item.timestamp).isSame(dayjs(), "day")
                              ? dayjs(item.timestamp).fromNow()
                              : dayjs(item.timestamp).format(
                                  "MMM D, YYYY h:mm A"
                                )}
                          </EverTg.Caption>
                        </div>

                        {/* Description */}
                        {item.details?.name && (
                          <div className="mb-4">
                            <EverTg.Caption className="text-gray-600 text-sm">
                              {item.details.name}
                            </EverTg.Caption>
                          </div>
                        )}

                        {/* User */}
                        <div className="flex items-center gap-3 mb-4">
                          <EverGroupAvatar
                            avatars={[
                              {
                                name: item.performed_by || "Unknown",
                                image: null,
                              },
                            ]}
                            size="small"
                          />
                          <EverTg.Caption className="text-gray-600 text-sm">
                            {item.performed_by || "Unknown"}
                          </EverTg.Caption>
                        </div>

                        {/* Show Details Button */}
                        {Object.keys(item.details || {}).length > 0 && (
                          <button
                            onClick={() => toggleExpanded(itemId)}
                            className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium"
                          >
                            {isExpanded ? (
                              <>
                                <ChevronUpIcon className="w-4 h-4" />
                                Hide details
                              </>
                            ) : (
                              <>
                                <ChevronDownIcon className="w-4 h-4" />
                                Show details
                              </>
                            )}
                          </button>
                        )}

                        {/* Expanded Details */}
                        {isExpanded && (
                          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                            {/* Special handling for calculated field events */}
                            {(item.action_type === "calculated_field_added" ||
                              item.action_type ===
                                "calculated_field_edited") && (
                              <div className="space-y-4">
                                {/* Field Name */}
                                {item.details?.name && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Field Name:
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-gray-700 ml-2 text-sm font-medium">
                                      {item.details.name}
                                    </EverTg.Caption>
                                  </div>
                                )}

                                {/* Data Type */}
                                {item.details?.data_type && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Data Type:
                                    </EverTg.Caption>
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                      {item.details.data_type}
                                    </span>
                                  </div>
                                )}

                                {/* Formula */}
                                {item.details?.formula_ast &&
                                Array.isArray(item.details.formula_ast) &&
                                item.details.formula_ast.length > 0 ? (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Formula:
                                    </EverTg.Caption>
                                    <div className="mt-1">
                                      <ReadOnlyExpression
                                        expressions={item.details.formula_ast}
                                      />
                                    </div>
                                  </div>
                                ) : item.details?.formula ? (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Formula:
                                    </EverTg.Caption>
                                    <div className="mt-1 p-3 bg-white border border-gray-200 rounded-md">
                                      <code className="text-sm text-gray-800 font-mono">
                                        {item.details.formula}
                                      </code>
                                    </div>
                                  </div>
                                ) : null}

                                {/* Changes (for edited events) */}
                                {item.details?.changes &&
                                  Object.keys(item.details.changes).length >
                                    0 && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                        Changes:
                                      </EverTg.Caption>
                                      <div className="space-y-3">
                                        {Object.entries(
                                          item.details.changes
                                        ).map(([changeKey, changeValue]) => (
                                          <div
                                            key={changeKey}
                                            className="bg-white border border-gray-200 rounded-md p-3"
                                          >
                                            <EverTg.Caption className="text-gray-600 font-medium text-xs uppercase tracking-wide mb-2">
                                              {changeKey === "name"
                                                ? "Field Name"
                                                : changeKey === "formula"
                                                ? "Formula"
                                                : changeKey}
                                            </EverTg.Caption>
                                            {changeKey === "formula" &&
                                            changeValue.before_ast &&
                                            Array.isArray(changeValue.before_ast) &&
                                            changeValue.before_ast.length > 0 &&
                                            changeValue.after_ast &&
                                            Array.isArray(changeValue.after_ast) &&
                                            changeValue.after_ast.length > 0 ? (
                                              <div className="space-y-3">
                                                <div>
                                                  <span className="text-xs text-red-600 font-medium mb-2 block">
                                                    Before:
                                                  </span>
                                                  <div className="bg-red-50 p-2 rounded">
                                                    <ReadOnlyExpression
                                                      expressions={
                                                        changeValue.before_ast
                                                      }
                                                    />
                                                  </div>
                                                </div>
                                                <div>
                                                  <span className="text-xs text-green-600 font-medium mb-2 block">
                                                    After:
                                                  </span>
                                                  <div className="bg-green-50 p-2 rounded">
                                                    <ReadOnlyExpression
                                                      expressions={
                                                        changeValue.after_ast
                                                      }
                                                    />
                                                  </div>
                                                </div>
                                              </div>
                                            ) : (
                                              <div className="space-y-2">
                                                <div className="flex items-center gap-2">
                                                  <span className="text-xs text-red-600 font-medium">
                                                    Before:
                                                  </span>
                                                  <code className="text-sm text-gray-700 bg-red-50 px-2 py-1 rounded">
                                                    {changeValue.before}
                                                  </code>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                  <span className="text-xs text-green-600 font-medium">
                                                    After:
                                                  </span>
                                                  <code className="text-sm text-gray-700 bg-green-50 px-2 py-1 rounded">
                                                    {changeValue.after}
                                                  </code>
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            )}

                            {/* Special handling for sheet renamed events */}
                            {item.action_type === "sheet_renamed" && (
                              <div className="space-y-4">
                                {/* Current Sheet Name */}
                                {item.details?.name && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Current Name:
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-gray-700 ml-2 text-sm font-medium">
                                      {item.details.name}
                                    </EverTg.Caption>
                                  </div>
                                )}

                                {/* Name Change */}
                                {item.details?.changes?.name && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                      Name Change:
                                    </EverTg.Caption>
                                    <div className="bg-white border border-gray-200 rounded-md p-3">
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                          <span className="text-xs text-red-600 font-medium">
                                            Before:
                                          </span>
                                          <span className="text-sm text-gray-700 bg-red-50 px-2 py-1 rounded">
                                            {item.details.changes.name.before}
                                          </span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <span className="text-xs text-green-600 font-medium">
                                            After:
                                          </span>
                                          <span className="text-sm text-gray-700 bg-green-50 px-2 py-1 rounded">
                                            {item.details.changes.name.after}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Special handling for adjustment events */}
                            {item.action_type === "adjustment_added" && (
                              <div className="space-y-4">
                                {/* Adjustment Number */}
                                {item.details?.adjustment_number && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Adjustment #:
                                    </EverTg.Caption>
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                      {item.details.adjustment_number}
                                      {item.details.sub_adjustment_number && (
                                        <span className="ml-1">
                                          ({item.details.sub_adjustment_number})
                                        </span>
                                      )}
                                    </span>
                                  </div>
                                )}

                                {/* Operation Type */}
                                {item.details?.operation && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Operation:
                                    </EverTg.Caption>
                                    <div className="ml-2 mt-1">
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        {item.details.operation}
                                      </span>
                                      {item.details.operation_description && (
                                        <div className="mt-1 text-sm text-gray-600">
                                          {item.details.operation_description}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Scope */}
                                {item.details?.scope && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Scope:
                                    </EverTg.Caption>
                                    <span className="ml-2 text-sm text-gray-700">
                                      {item.details.scope}
                                    </span>
                                  </div>
                                )}

                                {/* Comments */}
                                {item.details?.comments && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Comments:
                                    </EverTg.Caption>
                                    <div className="ml-2 mt-1 p-2 bg-white border border-gray-200 rounded-md">
                                      <EverTg.Caption className="text-sm text-gray-700">
                                        {item.details.comments}
                                      </EverTg.Caption>
                                    </div>
                                  </div>
                                )}

                                {/* Field Changes */}
                                {item.details?.field_changes && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                      Field Changes:
                                    </EverTg.Caption>
                                    <div className="bg-white border border-gray-200 rounded-md p-3">
                                      <div className="space-y-2">
                                        {Object.entries(
                                          item.details.field_changes
                                        ).map(([fieldName, fieldValue]) => (
                                          <div
                                            key={fieldName}
                                            className="flex items-center justify-between"
                                          >
                                            <span className="text-sm font-medium text-gray-600">
                                              {fieldName}:
                                            </span>
                                            <code className="text-sm text-gray-800 bg-gray-50 px-2 py-1 rounded">
                                              {fieldValue}
                                            </code>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                )}

                                {/* Field Changes Summary */}
                                {item.details?.field_changes_summary && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Changes:
                                    </EverTg.Caption>
                                    <span className="ml-2 text-sm text-gray-700">
                                      {item.details.field_changes_summary}
                                    </span>
                                  </div>
                                )}

                                {/* Split Operation Info */}
                                {item.details?.split_operation &&
                                  item.details?.records_created && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                        Split Result:
                                      </EverTg.Caption>
                                      <span className="ml-2 text-sm text-gray-700">
                                        Created {item.details.records_created}{" "}
                                        new record(s)
                                      </span>
                                    </div>
                                  )}

                                {/* Dangling Reason */}
                                {item.details?.dangling_reason && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Status:
                                    </EverTg.Caption>
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                      {item.details.dangling_reason}
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Special handling for sheet generation events */}
                            {item.action_type === "sheet_generated" && (
                              <div className="space-y-4">
                                {/* Sheet Name */}
                                {item.details?.name && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Sheet:
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-gray-700 ml-2 text-sm font-medium">
                                      {item.details.name}
                                    </EverTg.Caption>
                                  </div>
                                )}

                                {/* Generation Status */}
                                {item.details?.generation_status && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Status:
                                    </EverTg.Caption>
                                    <span
                                      className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                        item.details.generation_status ===
                                        "Completed"
                                          ? "bg-green-100 text-green-800"
                                          : "bg-yellow-100 text-yellow-800"
                                      }`}
                                    >
                                      {item.details.generation_status}
                                    </span>
                                  </div>
                                )}

                                {/* Generation Duration */}
                                {item.details?.generation_duration && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Duration:
                                    </EverTg.Caption>
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                      {item.details.generation_duration}
                                    </span>
                                  </div>
                                )}

                                {/* Generation Trigger */}
                                {item.details?.generation_trigger && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Triggered by:
                                    </EverTg.Caption>
                                    <div className="ml-2 mt-1 p-2 bg-white border border-gray-200 rounded-md">
                                      <EverTg.Caption className="text-sm text-gray-700">
                                        {item.details.generation_trigger}
                                      </EverTg.Caption>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Special handling for transformation events */}
                            {(item.action_type === "transformation_added" ||
                              item.action_type === "transformation_edited") && (
                              <div className="space-y-4">
                                {/* Transformation Type */}
                                {item.details?.type && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Type:
                                    </EverTg.Caption>
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                      {item.details.type}
                                    </span>
                                  </div>
                                )}

                                {/* Transformation Order */}
                                {item.details?.order !== undefined && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Order:
                                    </EverTg.Caption>
                                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                      #{item.details.order}
                                    </span>
                                  </div>
                                )}

                                {/* Description */}
                                {item.details?.description && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Description:
                                    </EverTg.Caption>
                                    <div className="ml-2 mt-1 p-2 bg-white border border-gray-200 rounded-md">
                                      <EverTg.Caption className="text-sm text-gray-700">
                                        {item.details.description}
                                      </EverTg.Caption>
                                    </div>
                                  </div>
                                )}

                                {/* JOIN specific details */}
                                {item.details?.type === "JOIN" && (
                                  <div className="space-y-3">
                                    {item.details.join_type && (
                                      <div>
                                        <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                          Join Type:
                                        </EverTg.Caption>
                                        <span className="ml-2 text-sm text-gray-700">
                                          {item.details.join_type}
                                        </span>
                                      </div>
                                    )}
                                    {item.details.with_sheet && (
                                      <div>
                                        <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                          With Sheet:
                                        </EverTg.Caption>
                                        <span className="ml-2 text-sm text-gray-700">
                                          {item.details.with_sheet}
                                        </span>
                                      </div>
                                    )}
                                    {item.details.join_conditions &&
                                      item.details.join_conditions.length >
                                        0 && (
                                        <div>
                                          <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                            Join Conditions:
                                          </EverTg.Caption>
                                          <div className="ml-2 space-y-1">
                                            {item.details.join_conditions.map(
                                              (condition, idx) => (
                                                <div
                                                  key={idx}
                                                  className="text-sm text-gray-700 bg-gray-50 px-2 py-1 rounded"
                                                >
                                                  <code>{condition}</code>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        </div>
                                      )}
                                  </div>
                                )}

                                {/* GROUP_BY specific details */}
                                {item.details?.type === "GROUP_BY" && (
                                  <div className="space-y-3">
                                    {item.details.group_by_columns &&
                                      item.details.group_by_columns.length >
                                        0 && (
                                        <div>
                                          <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                            Group By Columns:
                                          </EverTg.Caption>
                                          <div className="ml-2 flex flex-wrap gap-1">
                                            {item.details.group_by_columns.map(
                                              (col, idx) => (
                                                <span
                                                  key={idx}
                                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                >
                                                  {col}
                                                </span>
                                              )
                                            )}
                                          </div>
                                        </div>
                                      )}
                                    {item.details.aggregations &&
                                      item.details.aggregations.length > 0 && (
                                        <div>
                                          <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                            Aggregations:
                                          </EverTg.Caption>
                                          <div className="ml-2 space-y-1">
                                            {item.details.aggregations.map(
                                              (agg, idx) => (
                                                <div
                                                  key={idx}
                                                  className="text-sm text-gray-700 bg-gray-50 px-2 py-1 rounded"
                                                >
                                                  <code>{agg}</code>
                                                </div>
                                              )
                                            )}
                                          </div>
                                        </div>
                                      )}
                                  </div>
                                )}

                                {/* FILTER specific details */}
                                {item.details?.type === "FILTER" && (
                                  <div className="space-y-3">
                                    {item.details.filter_column && (
                                      <div>
                                        <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                          Column:
                                        </EverTg.Caption>
                                        <span className="ml-2 text-sm text-gray-700">
                                          {item.details.filter_column}
                                        </span>
                                      </div>
                                    )}
                                    {item.details.filter_operator && (
                                      <div>
                                        <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                          Operator:
                                        </EverTg.Caption>
                                        <span className="ml-2 text-sm text-gray-700">
                                          {item.details.filter_operator}
                                        </span>
                                      </div>
                                    )}
                                    {item.details.filter_value && (
                                      <div>
                                        <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                          Value:
                                        </EverTg.Caption>
                                        <span className="ml-2 text-sm text-gray-700">
                                          {item.details.filter_value}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                )}

                                {/* Other transformation types */}
                                {item.details?.type === "UNION" &&
                                  item.details.union_type && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                        Union Type:
                                      </EverTg.Caption>
                                      <span className="ml-2 text-sm text-gray-700">
                                        {item.details.union_type}
                                      </span>
                                    </div>
                                  )}

                                {item.details?.type === "SORT" &&
                                  item.details.sort_columns && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                        Sort Columns:
                                      </EverTg.Caption>
                                      <span className="ml-2 text-sm text-gray-700">
                                        {item.details.sort_columns.join(", ")}
                                      </span>
                                    </div>
                                  )}

                                {/* Changes (for edited events) */}
                                {item.action_type === "transformation_edited" &&
                                  item.details?.changes &&
                                  Object.keys(item.details.changes).length >
                                    0 && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm mb-2">
                                        Changes:
                                      </EverTg.Caption>
                                      <div className="space-y-3">
                                        {Object.entries(
                                          item.details.changes
                                        ).map(([changeKey, changeValue]) => (
                                          <div
                                            key={changeKey}
                                            className="bg-white border border-gray-200 rounded-md p-3"
                                          >
                                            <EverTg.Caption className="text-gray-600 font-medium text-xs uppercase tracking-wide mb-2">
                                              {changeKey === "type"
                                                ? "Transformation Type"
                                                : changeKey === "description"
                                                ? "Description"
                                                : changeKey === "join_type"
                                                ? "Join Type"
                                                : changeKey === "with_sheet"
                                                ? "Target Sheet"
                                                : changeKey ===
                                                  "group_by_columns"
                                                ? "Group By Columns"
                                                : changeKey === "aggregations"
                                                ? "Aggregations"
                                                : changeKey === "filter_column"
                                                ? "Filter Column"
                                                : changeKey ===
                                                  "filter_operator"
                                                ? "Filter Operator"
                                                : changeKey === "filter_value"
                                                ? "Filter Value"
                                                : changeKey}
                                            </EverTg.Caption>
                                            <div className="space-y-2">
                                              <div className="flex items-start gap-2">
                                                <span className="text-xs text-red-600 font-medium mt-1">
                                                  Before:
                                                </span>
                                                <div className="text-sm text-gray-700 bg-red-50 px-2 py-1 rounded flex-1">
                                                  {Array.isArray(
                                                    changeValue.before
                                                  ) ? (
                                                    <div className="space-y-1">
                                                      {changeValue.before.map(
                                                        (item, idx) => (
                                                          <div
                                                            key={idx}
                                                            className="text-xs"
                                                          >
                                                            {item}
                                                          </div>
                                                        )
                                                      )}
                                                    </div>
                                                  ) : (
                                                    <span>
                                                      {changeValue.before}
                                                    </span>
                                                  )}
                                                </div>
                                              </div>
                                              <div className="flex items-start gap-2">
                                                <span className="text-xs text-green-600 font-medium mt-1">
                                                  After:
                                                </span>
                                                <div className="text-sm text-gray-700 bg-green-50 px-2 py-1 rounded flex-1">
                                                  {Array.isArray(
                                                    changeValue.after
                                                  ) ? (
                                                    <div className="space-y-1">
                                                      {changeValue.after.map(
                                                        (item, idx) => (
                                                          <div
                                                            key={idx}
                                                            className="text-xs"
                                                          >
                                                            {item}
                                                          </div>
                                                        )
                                                      )}
                                                    </div>
                                                  ) : (
                                                    <span>
                                                      {changeValue.after}
                                                    </span>
                                                  )}
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            )}

                            {/* Special handling for permission events */}
                            {(item.action_type === "permission_created" ||
                              item.action_type === "permission_updated") && (
                              <div className="space-y-4">
                                {/* Permission Set Name */}
                                {item.details?.permission_set_name && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Permission Set:
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-gray-700 ml-2 text-sm font-medium">
                                      {item.details.permission_set_name}
                                    </EverTg.Caption>
                                  </div>
                                )}

                                {/* Users */}
                                {item.details?.users &&
                                  item.details.users.length > 0 && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                        Users ({item.details.users.length}):
                                      </EverTg.Caption>
                                      <div className="ml-2 mt-1 flex flex-wrap gap-1">
                                        {item.details.users.map(
                                          (user, index) => (
                                            <span
                                              key={index}
                                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                            >
                                              {user}
                                            </span>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}

                                {/* User Groups */}
                                {item.details?.user_groups &&
                                  item.details.user_groups.length > 0 && (
                                    <div>
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                        User Groups (
                                        {item.details.user_groups.length}):
                                      </EverTg.Caption>
                                      <div className="ml-2 mt-1 flex flex-wrap gap-1">
                                        {item.details.user_groups.map(
                                          (group, index) => (
                                            <span
                                              key={index}
                                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                                            >
                                              {group}
                                            </span>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}

                                {/* Row Filters */}
                                {item.details?.row_filters_count !==
                                  undefined && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Row Filters:
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-gray-700 ml-2 text-sm">
                                      {item.details.row_filters_count} filter
                                      {item.details.row_filters_count !== 1
                                        ? "s"
                                        : ""}{" "}
                                      applied
                                    </EverTg.Caption>
                                    {item.details.row_filters &&
                                      item.details.row_filters.length > 0 && (
                                        <div className="ml-2 mt-1 space-y-1">
                                          {item.details.row_filters.map(
                                            (filter, index) => (
                                              <div
                                                key={index}
                                                className="text-xs text-gray-600 bg-gray-50 px-2 py-1 rounded"
                                              >
                                                {filter.col_name}{" "}
                                                {filter.operator}{" "}
                                                {Array.isArray(filter.value)
                                                  ? filter.value.join(", ")
                                                  : filter.value}
                                              </div>
                                            )
                                          )}
                                          {item.details.row_filters_count >
                                            item.details.row_filters.length && (
                                            <div className="text-xs text-gray-500 italic">
                                              ... and{" "}
                                              {item.details.row_filters_count -
                                                item.details.row_filters
                                                  .length}{" "}
                                              more
                                            </div>
                                          )}
                                        </div>
                                      )}
                                  </div>
                                )}

                                {/* Hidden Columns */}
                                {item.details?.hidden_columns_count !==
                                  undefined && (
                                  <div>
                                    <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                      Hidden Columns:
                                    </EverTg.Caption>
                                    <EverTg.Caption className="text-gray-700 ml-2 text-sm">
                                      {item.details.hidden_columns_count} column
                                      {item.details.hidden_columns_count !== 1
                                        ? "s"
                                        : ""}{" "}
                                      hidden
                                    </EverTg.Caption>
                                    {item.details.hidden_columns &&
                                      item.details.hidden_columns.length >
                                        0 && (
                                        <div className="ml-2 mt-1 flex flex-wrap gap-1">
                                          {item.details.hidden_columns.map(
                                            (column, index) => (
                                              <span
                                                key={index}
                                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                                              >
                                                {column}
                                              </span>
                                            )
                                          )}
                                          {item.details.hidden_columns_count >
                                            item.details.hidden_columns
                                              .length && (
                                            <span className="text-xs text-gray-500 italic">
                                              ... and{" "}
                                              {item.details
                                                .hidden_columns_count -
                                                item.details.hidden_columns
                                                  .length}{" "}
                                              more
                                            </span>
                                          )}
                                        </div>
                                      )}
                                  </div>
                                )}

                                {/* Changes for permission_updated */}
                                {item.action_type === "permission_updated" &&
                                  item.details?.changes && (
                                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                      <EverTg.Caption className="text-blue-800 font-medium text-sm mb-3">
                                        Changes Made:
                                      </EverTg.Caption>
                                      <div className="space-y-3">
                                        {Object.entries(
                                          item.details.changes
                                        ).map(([changeKey, changeValue]) => (
                                          <div
                                            key={changeKey}
                                            className="text-sm"
                                          >
                                            <EverTg.Caption className="text-blue-700 font-medium capitalize">
                                              {changeKey.replace(/_/g, " ")}:
                                            </EverTg.Caption>
                                            <div className="ml-2 mt-1">
                                              {changeKey ===
                                                "permission_set_name" && (
                                                <div className="flex items-center gap-2">
                                                  <span className="text-xs text-red-600 font-medium">
                                                    Before:
                                                  </span>
                                                  <code className="text-sm text-gray-700 bg-red-50 px-2 py-1 rounded">
                                                    {changeValue.before}
                                                  </code>
                                                  <span className="text-xs text-green-600 font-medium">
                                                    After:
                                                  </span>
                                                  <code className="text-sm text-gray-700 bg-green-50 px-2 py-1 rounded">
                                                    {changeValue.after}
                                                  </code>
                                                </div>
                                              )}
                                              {(changeKey === "users" ||
                                                changeKey ===
                                                  "user_groups") && (
                                                <div className="space-y-2">
                                                  <div>
                                                    <span className="text-xs text-red-600 font-medium">
                                                      Before:
                                                    </span>
                                                    <div className="flex flex-wrap gap-1 mt-1">
                                                      {changeValue.before.map(
                                                        (item, index) => (
                                                          <span
                                                            key={index}
                                                            className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-red-50 text-red-700"
                                                          >
                                                            {item}
                                                          </span>
                                                        )
                                                      )}
                                                    </div>
                                                  </div>
                                                  <div>
                                                    <span className="text-xs text-green-600 font-medium">
                                                      After:
                                                    </span>
                                                    <div className="flex flex-wrap gap-1 mt-1">
                                                      {changeValue.after.map(
                                                        (item, index) => (
                                                          <span
                                                            key={index}
                                                            className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-50 text-green-700"
                                                          >
                                                            {item}
                                                          </span>
                                                        )
                                                      )}
                                                    </div>
                                                  </div>
                                                </div>
                                              )}
                                              {(changeKey === "row_filters" ||
                                                changeKey ===
                                                  "hidden_columns") && (
                                                <div className="flex items-center gap-2">
                                                  <span className="text-xs text-red-600 font-medium">
                                                    Before:
                                                  </span>
                                                  <span className="text-sm text-gray-700 bg-red-50 px-2 py-1 rounded">
                                                    {changeValue.before.count}{" "}
                                                    {changeKey === "row_filters"
                                                      ? "filters"
                                                      : "columns"}
                                                  </span>
                                                  <span className="text-xs text-green-600 font-medium">
                                                    After:
                                                  </span>
                                                  <span className="text-sm text-gray-700 bg-green-50 px-2 py-1 rounded">
                                                    {changeValue.after.count}{" "}
                                                    {changeKey === "row_filters"
                                                      ? "filters"
                                                      : "columns"}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            )}

                            {/* Default handling for other event types */}
                            {!(
                              item.action_type === "calculated_field_added" ||
                              item.action_type === "calculated_field_edited" ||
                              item.action_type === "sheet_renamed" ||
                              item.action_type === "adjustment_added" ||
                              item.action_type === "sheet_generated" ||
                              item.action_type === "transformation_added" ||
                              item.action_type === "transformation_edited" ||
                              item.action_type === "permission_created" ||
                              item.action_type === "permission_updated"
                            ) && (
                              <div>
                                {Object.entries(item.details || {}).map(
                                  ([key, value]) => (
                                    <div key={key} className="mb-3 last:mb-0">
                                      <EverTg.Caption className="text-gray-500 font-medium text-sm">
                                        {key}:
                                      </EverTg.Caption>
                                      <EverTg.Caption className="text-gray-700 ml-2 text-sm">
                                        {typeof value === "object"
                                          ? JSON.stringify(value, null, 2)
                                          : String(value)}
                                      </EverTg.Caption>
                                    </div>
                                  )
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SheetTimeline;
